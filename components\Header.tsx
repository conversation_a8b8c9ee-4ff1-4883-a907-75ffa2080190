'use client'

import { ReactNode } from 'react'
import { cn } from '@/lib/utils'
import BackButton from '@/components/BackButton'
import useScrolled from '@/hooks/useScrolled'
import getHeaderScrollConfig from '@/lib/getHeaderScrollConfig'
import ThemeSwitch from '@/components/ThemeSwitch'

type HeaderProps = {
  children: ReactNode
}

const Header = ({ children }: HeaderProps) => {
  const { scrolled } = useScrolled()
  const {
    headerPadding,
    headerBlur,
    headerOpacity,
    headerShadow,
    headerBorderColor,
    buttonScale,
    buttonPadding,
    buttonFrontSize,
    iconSize,
  } = getHeaderScrollConfig(scrolled)

  return (
    <header
      className={cn(
        'flex items-center justify-between gap-4 sticky top-0 z-50 border-b-[1px] transition-all duration-400 **:duration-400 ',
        headerPadding,
        headerBlur,
        headerOpacity,
        headerShadow,
        headerBorderColor
      )}
    >
      <BackButton
        buttonClassName={cn(buttonScale, buttonPadding, buttonFrontSize)}
        iconClassName={iconSize}
      />
      {children}
      <ThemeSwitch className="absolute -bottom-12 right-4 z-50" size="sm" />
    </header>
  )
}

export default Header
