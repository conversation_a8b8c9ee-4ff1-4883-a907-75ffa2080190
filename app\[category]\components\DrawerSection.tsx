import { ReactNode } from 'react'
import { Badge } from '@/components/UI/badge'

type Props = {
  title: string
  children: ReactNode
  badgeText?: string | number
}

const DrawerSection = ({ title, children, badgeText }: Props) => (
  <div className="space-y-4">
    <h3 className="text-base font-semibold text-primary-text">
      {title}
      {badgeText && (
        <Badge variant="drawer" size="sm" className="ml-2">
          {badgeText}
        </Badge>
      )}
    </h3>
    {children}
  </div>
)

export default DrawerSection
