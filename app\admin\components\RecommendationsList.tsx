'use client'

import { useMemo, useState } from 'react'
import <PERSON>Header from './RecommendationsListHeader'
import FilterCard from './FilterCard'
import RecommendationsGridView from './RecommendationsGridView'
import { Recommendation } from '@/types/types'
import { RECOMMENDATION_CATEGORIES } from '@/data/recommendations'
import RecommendationsTableView from './RecommendationsTableView'
import { useDebounce } from '@uidotdev/usehooks'

type Props = {
  recommendations: Recommendation[]
}

const RecommendationsList = ({ recommendations }: Props) => {
  const [searchQuery, setSearchQuery] = useState('')
  const debouncedSearchQuery = useDebounce(searchQuery, 500)
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid')

  const filteredRecommendations = useMemo(
    () =>
      recommendations.filter(rec => {
        if (!rec) return false

        if (
          selectedCategory !== 'all' &&
          rec.category.toLowerCase() !== selectedCategory.toLowerCase()
        )
          return false

        return (
          rec.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
          rec.category.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
          rec.description.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
          rec.address.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
          (Array.isArray(rec.tags) &&
            rec.tags.some(
              tag => tag && tag.toLowerCase().includes(debouncedSearchQuery.toLowerCase())
            ))
        )
      }),
    [debouncedSearchQuery, recommendations, selectedCategory]
  )

  const stats = useMemo(
    () => ({
      total: recommendations.length,
      byCategory: RECOMMENDATION_CATEGORIES.reduce(
        (acc, cat) => {
          acc[cat] = recommendations.filter(rec => rec && rec.category === cat).length
          return acc
        },
        {} as Record<string, number>
      ),
    }),
    [recommendations]
  )

  return (
    <main className="relative max-w-screen-xl mx-auto py-6 lg:px-6 space-y-6 flex-1">
      <ListHeader stats={stats} numberOfFilteredResults={filteredRecommendations.length} />
      <FilterCard
        stats={stats}
        viewMode={viewMode}
        setViewMode={setViewMode}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        selectedCategory={selectedCategory}
        setSelectedCategory={setSelectedCategory}
      />
      {viewMode === 'grid' ? (
        <RecommendationsGridView recommendations={filteredRecommendations} />
      ) : (
        <RecommendationsTableView recommendations={filteredRecommendations} />
      )}
    </main>
  )
}
export default RecommendationsList
