import { FilterState, Recommendation } from '@/types/types'
import { applyFilters } from '@/lib/filterUtils'

const SEARCHABLE_FIELDS = ['name', 'description', 'category', 'address'] as const

const SEARCHABLE_ARRAYS = ['tags', 'menuHighlights', 'amenities'] as const

const RELEVANCE_SCORES = {
  EXACT_NAME: 20,
  NAME_MATCH: 10,
  CATEGORY_MATCH: 7,
  TAG_MATCH: 5,
  DESCRIPTION_MATCH: 3,
} as const

/**
 * Search recommendations based on query string
 * Searches across name, description, tags, category, address, and menu highlights
 */
export function searchRecommendations(
  recommendations: Recommendation[],
  searchQuery: string
): Recommendation[] {
  if (!searchQuery.trim()) {
    return recommendations
  }

  const query = searchQuery.toLowerCase().trim()

  return recommendations.filter(rec => {
    const hasStringMatch = SEARCHABLE_FIELDS.some(
      field => typeof rec[field] === 'string' && rec[field].toLowerCase().includes(query)
    )
    if (hasStringMatch) return true

    return SEARCHABLE_ARRAYS.some(field =>
      rec[field]?.some(item => item.toLowerCase().includes(query))
    )
  })
}

/**
 * Calculate relevance score for a recommendation
 */
function calculateRelevanceScore(rec: Recommendation, query: string): number {
  const queryLower = query.toLowerCase()
  let score = 0
  if (rec.name.toLowerCase() === queryLower) {
    score += RELEVANCE_SCORES.EXACT_NAME
  } else if (rec.name.toLowerCase().includes(queryLower)) {
    score += RELEVANCE_SCORES.NAME_MATCH
  }

  if (rec.category.toLowerCase().includes(queryLower)) {
    score += RELEVANCE_SCORES.CATEGORY_MATCH
  }

  const tagMatches = rec.tags.filter(tag => tag.toLowerCase().includes(queryLower)).length
  score += tagMatches * RELEVANCE_SCORES.TAG_MATCH

  if (rec.description.toLowerCase().includes(queryLower)) {
    score += RELEVANCE_SCORES.DESCRIPTION_MATCH
  }

  return score
}

/**
 * Sort search results by relevance
 */
export function sortSearchResults(
  recommendations: Recommendation[],
  searchQuery: string
): Recommendation[] {
  if (!searchQuery.trim()) {
    return [...recommendations].sort((a, b) => b.rating - a.rating)
  }

  return [...recommendations].sort((a, b) => {
    const scoreA = calculateRelevanceScore(a, searchQuery)
    const scoreB = calculateRelevanceScore(b, searchQuery)

    return scoreA === scoreB ? b.rating - a.rating : scoreB - scoreA
  })
}

/**
 * Combined search and filter function
 */
export function searchAndFilterRecommendations(
  recommendations: Recommendation[],
  searchQuery: string,
  filters: FilterState
): Recommendation[] {
  let result = applyFilters(recommendations, filters)

  if (searchQuery.trim()) {
    result = searchRecommendations(result, searchQuery)
  }

  return sortSearchResults(result, searchQuery)
}
