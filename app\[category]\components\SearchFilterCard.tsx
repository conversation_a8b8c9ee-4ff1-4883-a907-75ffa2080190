import { Button } from '@/components/UI/button'
import GlassCard from '@/components/GlassCard'
import { defaultFilterState, getActiveFiltersCount } from '@/lib/filterUtils'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/UI/badge'
import pluralize from 'pluralize'
import { useQueryStates } from 'nuqs'
import { queryFiltersSearchParams } from '@/lib/searchParams'
import SearchResultsSummary from './SearchResultsSummary'

type Props = {
  numberOfRecommendations: number
  gradient: string
}

const SearchFilterCard = ({ numberOfRecommendations, gradient }: Props) => {
  const [{ q: searchQuery, ...filters }, setQueryFilters] = useQueryStates(
    queryFiltersSearchParams,
    {
      history: 'replace',
      shallow: false,
    }
  )
  const activeFiltersCount = getActiveFiltersCount(filters)

  if (!searchQuery.trim().length && activeFiltersCount == 0) return null

  const handleClearSearch = () => setQueryFilters({ q: '' })
  const handleClearFilters = () => setQueryFilters(defaultFilterState)

  return (
    <GlassCard className="flex-col items-center gap-3">
      <SearchResultsSummary
        numberOfRecommendations={numberOfRecommendations}
        searchQuery={searchQuery}
        activeFiltersCount={activeFiltersCount}
      />
      <div className="flex items-center justify-center gap-2 flex-wrap">
        {searchQuery && (
          <div className="flex text-accent-text bg-card-bg backdrop-blur-xl border-[1px] border-glass-border rounded-full px-3 py-1 shadow-lg">
            <span>Searching for ”</span>
            <span className="flex-1 font-medium line-clamp-1 break-all">{searchQuery}</span>
            <span>”</span>
          </div>
        )}
        {activeFiltersCount > 0 && (
          <Badge
            className={cn(
              'text-xs bg-gradient-to-r text-white border-transparent border-x-0',
              gradient
            )}
          >
            {pluralize('filter', activeFiltersCount, true)}
          </Badge>
        )}
      </div>
      <div className="flex gap-2 justify-center">
        {searchQuery && (
          <Button
            variant="frostedGlass"
            size="sm"
            onClick={handleClearSearch}
            className={'duration-300'}
          >
            Clear search
          </Button>
        )}
        {activeFiltersCount > 0 && (
          <Button
            variant="frostedGlass"
            size="sm"
            onClick={handleClearFilters}
            className={'duration-300'}
          >
            Clear filters
          </Button>
        )}
      </div>
    </GlassCard>
  )
}

export default SearchFilterCard
