import { FormControl, FormField, FormItem, FormMessage } from '@/components/UI/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/UI/select'
import FormLabel from '@/components/FormLabel'
import { Control, FieldValues, Path } from 'react-hook-form'
import { ZodObject } from 'zod'
import { cn } from '@/lib/utils'

type Props<T extends FieldValues> = {
  control: Control<T>
  name: Path<T>
  options: Record<string | number, string> | string[]
  placeholder?: string
  label?: string
  schema?: ZodObject
}

const SelectField = <T extends FieldValues>({
  control,
  name,
  label,
  placeholder,
  options,
  schema,
}: Props<T>) => (
  <FormField
    control={control}
    name={name}
    render={({ field: { value, onChange }, fieldState: { error } }) => (
      <FormItem>
        <FormLabel label={label} value={name} schema={schema} />
        <FormControl>
          <Select
            onValueChange={val => {
              if (typeof value === 'string') onChange(val)
              else onChange(Number(val))
            }}
            defaultValue={String(value)}
          >
            <SelectTrigger id={name} className={cn('w-full', error && 'border-red-500')}>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {Array.isArray(options)
                ? options.map(option => (
                    <SelectItem key={option} value={option}>
                      {option}
                    </SelectItem>
                  ))
                : Object.entries(options).map(([key, val]) => (
                    <SelectItem key={key} value={key}>
                      {val}
                    </SelectItem>
                  ))}
            </SelectContent>
          </Select>
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
)

export default SelectField
