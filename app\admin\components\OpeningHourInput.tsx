import { Label } from '@/components/UI/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/UI/select'
import { TIME_OPTIONS } from '@/data/date'
import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'

type Props = {
  label: string
  labelIcon: LucideIcon
  labelIconColor: string
  name: string
  value: string
  onChange: (time: string) => void
  error?: string
}

const OpeningHourInput = ({
  label,
  labelIcon: Icon,
  labelIconColor,
  name,
  value,
  onChange,
  error,
}: Props) => (
  <div className="flex flex-col gap-1">
    <Label htmlFor={name} className="text-xs text-gray-700 flex items-center gap-1">
      <Icon size={12} className={labelIconColor} /> {label}
    </Label>
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger
        id={name}
        className={cn('text-xs bg-white w-full', error ? 'border-red-300' : 'border-gray-300')}
      >
        <SelectValue />
      </SelectTrigger>
      <SelectContent className="max-h-40">
        {TIME_OPTIONS.map(time => (
          <SelectItem key={time} value={time} className="text-xs">
            {time}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  </div>
)

export default OpeningHourInput
