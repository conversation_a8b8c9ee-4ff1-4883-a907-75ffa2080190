import { Card } from '@/components/UI/card'
import { Badge } from '@/components/UI/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/UI/table'
import { ScrollArea } from '@/components/UI/scroll-area'
import { Star } from 'lucide-react'
import { Recommendation } from '@/types/types'
import { PRICE_LEVEL_MAP } from '@/data/recommendations'
import DeleteRecommendationButton from './DeleteRecommendationButton'
import EditRecommendationButton from './EditRecommendationButton'
import { cn } from '@/lib/utils'
import { getCategoryDetailsByRecommendationId } from '@/lib/categoryHelper'

type Props = {
  recommendations: Recommendation[]
}

const RecommendationsTableView = ({ recommendations }: Props) => (
  <Card className="py-0 animate-fade-up rounded-md">
    <ScrollArea className="h-128">
      <Table>
        <TableHeader>
          <TableRow className="*:text-center">
            <TableHead>Name</TableHead>
            <TableHead className="max-md:hidden">Category</TableHead>
            <TableHead>Rating</TableHead>
            <TableHead className="max-md:hidden">Price</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {recommendations.map(recommendation => {
            try {
              const { id, name, category, description, rating, priceLevel } = recommendation
              const { gradient } = getCategoryDetailsByRecommendationId(id)

              return (
                <TableRow key={id}>
                  <TableCell className="py-3 flex items-center gap-3">
                    <div
                      className={cn(
                        'size-6 rounded-md bg-gradient-to-r flex items-center justify-center flex-shrink-0',
                        gradient
                      )}
                    >
                      <span className="text-white font-medium text-xs">{category.charAt(0)}</span>
                    </div>
                    <div>
                      <p className="font-medium">{name}</p>
                      <p className="text-gray-600 md:max-w-xs xl:max-w-full truncate max-md:hidden">
                        {description}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell className="text-center max-md:hidden">
                    <Badge variant="outline" className="rounded-md">
                      {category}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center justify-center gap-1">
                      <Star className="size-4 text-yellow-500 fill-current" />
                      <span className="font-medium">{rating}</span>
                    </div>
                  </TableCell>
                  <TableCell className="text-center max-md:hidden">
                    <Badge variant="outline" className="rounded-md">
                      {PRICE_LEVEL_MAP[priceLevel]}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center justify-center gap-1">
                      <EditRecommendationButton recommendation={recommendation} />
                      <DeleteRecommendationButton id={id} />
                    </div>
                  </TableCell>
                </TableRow>
              )
            } catch (error) {
              console.error(error)
              return null
            }
          })}
        </TableBody>
      </Table>
    </ScrollArea>
  </Card>
)

export default RecommendationsTableView
