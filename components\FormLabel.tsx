import { ComponentProps } from 'react'
import { ZodOptional, ZodObject, ZodNullable } from 'zod'
import { FormLabel as ShadFormLabel } from '@/components/UI/form'

type Props = {
  value: string
  label?: string
  schema?: ZodObject
} & ComponentProps<typeof ShadFormLabel>

const FormLabel = ({ label, value, schema, ...props }: Props) => {
  if (!label) return null

  const requiredFields =
    schema &&
    Object.entries(schema.shape).filter(
      ([, val]) => val && !(val instanceof ZodOptional) && !(val instanceof ZodNullable)
    )

  const isRequired = requiredFields && requiredFields.some(([key]) => key === value)

  return (
    <ShadFormLabel htmlFor={value} {...props}>
      {label} {!!isRequired && '*'}
    </ShadFormLabel>
  )
}

export default FormLabel
