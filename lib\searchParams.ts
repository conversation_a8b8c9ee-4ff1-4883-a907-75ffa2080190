import {
  parseAsFloat,
  createSearchParamsCache,
  parseAsArrayOf,
  parseAsInteger,
  parseAsString,
} from 'nuqs/server'
import { z } from 'zod'

export const filterSchema = z.object({
  priceLevel: z.array(z.number()),
  rating: z.number().min(0).max(5),
  tags: z.array(z.string()),
  categories: z.array(z.string()),
  travelTime: z.number(),
  amenities: z.array(z.string()),
})

export type FilterFormType = z.infer<typeof filterSchema>

export const queryFiltersSearchParams = {
  q: parseAsString.withDefault(''),
  priceLevel: parseAsArrayOf(parseAsInteger).withDefault([]),
  rating: parseAsFloat.withDefault(0),
  tags: parseAsArrayOf(parseAsString).withDefault([]),
  categories: parseAsArrayOf(parseAsString).withDefault([]),
  travelTime: parseAsInteger.withDefault(0),
  amenities: parseAsArrayOf(parseAsString).withDefault([]),
} as const

export const searchParamsCache = createSearchParamsCache(queryFiltersSearchParams)
