import FormLabel from '@/components/FormLabel'
import { FormControl, FormField, FormItem, FormMessage } from '@/components/UI/form'
import { Input } from '@/components/UI/input'
import { Textarea } from '@/components/UI/textarea'
import { cn } from '@/lib/utils'
import { ComponentProps, HTMLAttributes } from 'react'
import { Control, FieldValues, Path } from 'react-hook-form'
import { ZodObject } from 'zod'

type Props<T extends FieldValues> = {
  as?: 'input' | 'textarea'
  control: Control<T>
  name: Path<T>
  label?: string
  schema?: ZodObject
} & ComponentProps<typeof Input | typeof Textarea>

const InputField = <T extends FieldValues>({
  as = 'input',
  control,
  name,
  label,
  schema,
  ...props
}: Props<T>) => (
  <FormField
    control={control}
    name={name}
    render={({ field }) => (
      <FormItem>
        <FormLabel label={label} value={name} schema={schema} />
        <FormControl>
          {as === 'textarea' ? (
            <Textarea
              {...field}
              className={cn('break-all', props.className)}
              {...(props as HTMLAttributes<HTMLTextAreaElement>)}
            />
          ) : (
            <Input {...field} {...(props as HTMLAttributes<HTMLInputElement>)} />
          )}
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
)

export default InputField
