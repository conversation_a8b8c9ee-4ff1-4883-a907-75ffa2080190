import IconButton from '@/components/IconButton'
import { Badge } from '@/components/UI/badge'
import { Label } from '@/components/UI/label'
import { Switch } from '@/components/UI/switch'
import { DAYS, NINE_TO_FIVE_HOUR_DAY } from '@/data/date'
import { cn } from '@/lib/utils'
import { DayHours, OpeningHoursData } from '@/types/types'
import { Copy } from 'lucide-react'
import React from 'react'
import { DayHoursSchema, OpeningHoursSchema } from './OpeningHoursForm'

type Props = {
  day: keyof OpeningHoursData
  days: OpeningHoursSchema
  dayIndex: number
  dayHours: DayHours
  updateDayHours: (day: keyof OpeningHoursData, dayHours: DayHoursSchema) => void
  error?: string
}

const OpeningHourDayHeader = ({ day, days, dayIndex, dayHours, updateDayHours, error }: Props) => {
  const copyFromPreviousDay = () => {
    if (dayIndex > 0) {
      const previousDay = DAYS[dayIndex - 1]
      const previousHours = days[previousDay.label]
      const copiedHours = {
        ...previousHours,
        periods: previousHours.periods.map(p => ({
          ...p,
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        })),
      }
      updateDayHours(day, copiedHours)
    }
  }

  return (
    <div className={cn('flex justify-between sm:grid items-center grid-cols-3')}>
      <h4 className={cn('capitalize font-medium text-sm text-slate-900', error && 'text-red-700')}>
        {day}
      </h4>
      <div className="place-self-center flex items-center gap-2">
        <div className="flex items-center gap-1">
          <Switch
            id={`${dayIndex}-24h`}
            checked={dayHours.is24Hours}
            onCheckedChange={checked =>
              updateDayHours(day, {
                is24Hours: checked,
                periods: [],
              })
            }
            className="data-[state=checked]:bg-purple-600 scale-75"
          />
          <Label htmlFor={`${dayIndex}-24h`} className="text-xs text-gray-700">
            24h
          </Label>
        </div>
        {dayIndex > 0 && (dayHours.is24Hours || dayHours.periods.length !== 0) && (
          <IconButton
            Icon={Copy}
            variant="ghost"
            size="sm"
            onClick={copyFromPreviousDay}
            className="h-auto px-2 scale-75"
          />
        )}
      </div>
      <div className="place-self-end flex items-center gap-1">
        <Switch
          checked={dayHours.periods?.length !== 0}
          onCheckedChange={checked =>
            updateDayHours(day, {
              is24Hours: checked && false,
              periods: checked ? NINE_TO_FIVE_HOUR_DAY.periods : [],
            })
          }
          className="data-[state=checked]:bg-blue-600 scale-75"
        />
        <Badge
          variant="outline"
          className={cn(
            'py-0 px-1 text-gray-600',
            dayHours.periods?.length && 'bg-green-100 text-green-800 border-green-200'
          )}
        >
          {dayHours.periods?.length ? 'On' : 'Off'}
        </Badge>
      </div>
    </div>
  )
}

export default OpeningHourDayHeader
