import FormLabel from '@/components/FormLabel'
import IconButton from '@/components/IconButton'
import { Card, CardHeader, CardTitle, CardDescription, CardAction } from '@/components/UI/card'
import { FormField, FormItem, FormControl, FormMessage } from '@/components/UI/form'
import { Clock } from 'lucide-react'
import React from 'react'
import { Control, FieldValues, Path } from 'react-hook-form'
import { ZodObject } from 'zod'
import OpeningHoursFormDialog from '@/app/admin/components/OpeningHoursFormDialog'
import { generateHoursString } from '@/lib/openingHoursHelper'

type Props<T extends FieldValues> = {
  control: Control<T>
  schema: ZodObject
  description?: string | null
}

const OpeningHoursField = <T extends FieldValues>({ control, description, schema }: Props<T>) => (
  <FormField
    control={control}
    name={'openingHours' as Path<T>}
    render={({ field: { value, onChange } }) => (
      <FormItem>
        <FormLabel label="Opening Hours" value="openingHours" schema={schema} />
        <FormControl>
          <Card className="p-4 bg-gray-50 shadow-xs text-sm">
            <CardHeader className="p-0">
              <CardTitle>Preview</CardTitle>
              <CardDescription className="text-gray-600 truncate">
                {[(value && generateHoursString(value)) || '', description && description]
                  .filter(Boolean)
                  .join(' - ') || 'No hours set'}
              </CardDescription>
              <CardAction>
                <OpeningHoursFormDialog value={value} onChange={onChange}>
                  <IconButton
                    variant="outline"
                    Icon={Clock}
                    text={value ? 'Edit Hours' : 'Set Hours'}
                    className="rounded-lg"
                  />
                </OpeningHoursFormDialog>
              </CardAction>
            </CardHeader>
          </Card>
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
)

export default OpeningHoursField
