import { generateHoursString } from '@/lib/openingHoursHelper'
import { cn } from '@/lib/utils'
import { OpeningHoursData } from '@/types/types'
import { AlertTriangle, CheckCircle } from 'lucide-react'
import React from 'react'

type Props = {
  openHours: OpeningHoursData
  hasErrors: boolean
}

const OpeningHoursPreview = ({ openHours, hasErrors }: Props) => (
  <div className="flex flex-col gap-1">
    <h3 className="text-sm font-medium">Preview</h3>
    <div
      className={cn(
        'p-2 rounded border',
        hasErrors ? 'bg-red-50 border-red-200' : 'bg-blue-50 border-blue-200'
      )}
    >
      <div className="flex items-center gap-2">
        {hasErrors ? (
          <AlertTriangle size={16} className="text-red-600" />
        ) : (
          <CheckCircle size={16} className="text-green-600" />
        )}
        <p className={cn('text-xs font-medium', hasErrors ? 'text-red-800' : 'text-blue-800')}>
          {hasErrors ? 'Please fix errors above' : generateHoursString(openHours)}
        </p>
      </div>
    </div>
  </div>
)

export default OpeningHoursPreview
