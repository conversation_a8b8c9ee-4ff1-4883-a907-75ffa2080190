import { DayHours, OpeningHoursData, WeekDay } from '@/types/types'
import fill from 'lodash/fill'

export const DAYS = [
  { label: 'monday', short: 'Mon' },
  { label: 'tuesday', short: 'Tue' },
  { label: 'wednesday', short: 'Wed' },
  { label: 'thursday', short: 'Thu' },
  { label: 'friday', short: 'Fri' },
  { label: 'saturday', short: 'Sat' },
  { label: 'sunday', short: 'Sun' },
] as const

export const WEEKDAYS: WeekDay[] = DAYS.map(d => d.label)

export const TIME_OPTIONS = fill(Array(49), '').map((_, idx) => {
  const hour = idx ? Math.floor(idx / 2) : '0'
  const minute = idx % 2 === 0 ? '00' : '30'
  return `${hour.toString().padStart(2, '0')}:${minute}`
})

export const NINE_TO_FIVE_HOUR_DAY = {
  is24Hours: false,
  periods: [{ openTime: '09:00', closeTime: '17:00' }],
}

export const CLOSED_DAY: DayHours = { is24Hours: false, periods: [] }
export const TWENTY_FOUR_HOUR_DAY: DayHours = { is24Hours: true, periods: [] }

export const PRESETS: Record<string, OpeningHoursData> = {
  Business: {
    monday: NINE_TO_FIVE_HOUR_DAY,
    tuesday: NINE_TO_FIVE_HOUR_DAY,
    wednesday: NINE_TO_FIVE_HOUR_DAY,
    thursday: NINE_TO_FIVE_HOUR_DAY,
    friday: NINE_TO_FIVE_HOUR_DAY,
    saturday: CLOSED_DAY,
    sunday: CLOSED_DAY,
  },
  Closed: {
    monday: CLOSED_DAY,
    tuesday: CLOSED_DAY,
    wednesday: CLOSED_DAY,
    thursday: CLOSED_DAY,
    friday: CLOSED_DAY,
    saturday: CLOSED_DAY,
    sunday: CLOSED_DAY,
  },
  Restaurant: {
    monday: {
      is24Hours: false,
      periods: [{ openTime: '11:00', closeTime: '22:00' }],
    },
    tuesday: {
      is24Hours: false,
      periods: [{ openTime: '11:00', closeTime: '22:00' }],
    },
    wednesday: {
      is24Hours: false,
      periods: [{ openTime: '11:00', closeTime: '22:00' }],
    },
    thursday: {
      is24Hours: false,
      periods: [{ openTime: '11:00', closeTime: '22:00' }],
    },
    friday: {
      is24Hours: false,
      periods: [{ openTime: '11:00', closeTime: '23:00' }],
    },
    saturday: {
      is24Hours: false,
      periods: [{ openTime: '11:00', closeTime: '23:00' }],
    },
    sunday: {
      is24Hours: false,
      periods: [{ openTime: '12:00', closeTime: '21:00' }],
    },
  },
  'Lunch Break': {
    monday: {
      is24Hours: false,
      periods: [
        { openTime: '09:00', closeTime: '12:00' },
        { openTime: '14:00', closeTime: '18:00' },
      ],
    },
    tuesday: {
      is24Hours: false,
      periods: [
        { openTime: '09:00', closeTime: '12:00' },
        { openTime: '14:00', closeTime: '18:00' },
      ],
    },
    wednesday: {
      is24Hours: false,
      periods: [
        { openTime: '09:00', closeTime: '12:00' },
        { openTime: '14:00', closeTime: '18:00' },
      ],
    },
    thursday: {
      is24Hours: false,
      periods: [
        { openTime: '09:00', closeTime: '12:00' },
        { openTime: '14:00', closeTime: '18:00' },
      ],
    },
    friday: {
      is24Hours: false,
      periods: [
        { openTime: '09:00', closeTime: '12:00' },
        { openTime: '14:00', closeTime: '18:00' },
      ],
    },
    saturday: CLOSED_DAY,
    sunday: CLOSED_DAY,
  },
  Nightlife: {
    monday: CLOSED_DAY,
    tuesday: CLOSED_DAY,
    wednesday: {
      is24Hours: false,
      periods: [{ openTime: '20:00', closeTime: '02:00' }],
    },
    thursday: {
      is24Hours: false,
      periods: [{ openTime: '20:00', closeTime: '02:00' }],
    },
    friday: {
      is24Hours: false,
      periods: [{ openTime: '20:00', closeTime: '03:00' }],
    },
    saturday: {
      is24Hours: false,
      periods: [{ openTime: '20:00', closeTime: '03:00' }],
    },
    sunday: {
      is24Hours: false,
      periods: [{ openTime: '20:00', closeTime: '01:00' }],
    },
  },
  '24/7': {
    monday: TWENTY_FOUR_HOUR_DAY,
    tuesday: TWENTY_FOUR_HOUR_DAY,
    wednesday: TWENTY_FOUR_HOUR_DAY,
    thursday: TWENTY_FOUR_HOUR_DAY,
    friday: TWENTY_FOUR_HOUR_DAY,
    saturday: TWENTY_FOUR_HOUR_DAY,
    sunday: TWENTY_FOUR_HOUR_DAY,
  },
}
