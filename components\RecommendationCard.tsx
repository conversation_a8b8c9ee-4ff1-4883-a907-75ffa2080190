'use client'

import { useRouter } from 'next/navigation'
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/UI/card'
import { Badge } from '@/components/UI/badge'
import { MapPin, Clock, Star } from 'lucide-react'
import ImageWithFallback from '@/components/ImageWithFallback'
import { CategoryDetails, Recommendation } from '@/types/types'
import { cn } from '@/lib/utils'
import StarRating from '@/components/StarRating'
import PriceLevel from '@/components/PriceLevel'
import RecommendationImageOverlay from '@/components/RecommendationImageOverlay'
import TagList from '@/components/BadgeTagList'
import IconBadge from '@/components/IconBadge'
import { generateHoursString } from '@/lib/openingHoursHelper'
import { useMemo } from 'react'
import { getOpenStatus } from '@/lib/recommendationHelper'

type Props = {
  recommendation: Recommendation
  categoryDetails: Pick<CategoryDetails, 'id' | 'gradient' | 'icon'>
  index?: number
  showDetails?: boolean
  isAdminView?: boolean
}

const RecommendationCard = ({
  categoryDetails: { id: categoryId, gradient, icon },
  recommendation,
  recommendation: {
    id,
    name,
    description,
    rating,
    priceLevel,
    address,
    openingHours,
    openingHoursInfo,
    images,
    tags,
  },
  index,
  showDetails = false,
  isAdminView = false,
}: Props) => {
  const router = useRouter()
  const isOpen = showDetails ? getOpenStatus(openingHours) : null

  const openHoursString = useMemo(
    () => openingHours && generateHoursString(openingHours),
    [openingHours]
  )

  const handleClick = () => !isAdminView && router.push(`/${categoryId}/${id}`)

  return (
    <Card
      className={cn(
        'group overflow-hidden border-0 backdrop-blur-sm shadow-lg hoverActive:shadow-2xl transition-[background-color,scale,box-shadow] duration-700 hoverActive:scale-103 cursor-pointer bg-card-bg hoverActive:bg-card-hover p-0 animation-duration-500 opacity-0',
        showDetails
          ? 'gap-0 animate-fade-up'
          : 'flex-row items-start gap-5 animate-fade-up-transform'
      )}
      onClick={handleClick}
      style={{
        animationDelay: `${(index ?? 0) * 150}ms`,
      }}
    >
      {showDetails && (
        <div className="relative aspect-4/3 overflow-hidden">
          <ImageWithFallback
            src={images[0]}
            alt={name}
            className="w-full h-full object-cover group-hoverActive:scale-110 transition-transform duration-700"
          />
          <RecommendationImageOverlay
            recommendation={recommendation}
            gradient={gradient}
            icon={icon}
            showLikeButton
            showActionButton={isAdminView}
          />
        </div>
      )}
      <div className={cn('relative p-5 flex flex-1', !showDetails && 'gap-5')}>
        <div
          className={cn(
            'absolute -inset-1 bg-gradient-to-r rounded-lg blur opacity-0 group-hoverActive:opacity-20 transition-opacity duration-500 pointer-events-none',
            gradient
          )}
        />
        {!showDetails && index !== undefined && (
          <Badge
            className={cn(
              'size-10 text-white text-sm group-hoverActive:scale-110 transition-everything bg-gradient-to-br duration-300',
              gradient
            )}
          >
            {index + 1}
          </Badge>
        )}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -translate-x-full group-hoverActive:translate-x-full transition-transform duration-1000 ease-out pointer-events-none" />
        <div className={cn('relative flex flex-col *:p-0 flex-1', showDetails ? 'gap-4' : 'gap-3')}>
          <CardHeader className="**:duration-300">
            <CardTitle
              className={cn(
                'row-span-2 line-clamp-1 transition-colors text-primary-text text-xl group-hoverActive:text-transparent bg-clip-text bg-gradient-to-r',
                gradient
              )}
            >
              {name}
            </CardTitle>
            <CardAction>
              {showDetails ? (
                isOpen !== null && (
                  <Badge
                    variant="secondary"
                    className={cn(
                      'py-1 rounded-md',
                      isOpen
                        ? 'border-emerald-200 text-emerald-700 bg-emerald-50'
                        : 'border-red-200 text-red-700 bg-red-50'
                    )}
                  >
                    {isOpen ? 'Open now' : 'Closed'}
                  </Badge>
                )
              ) : (
                <IconBadge
                  text={rating.toString()}
                  Icon={Star}
                  variant="card"
                  className={cn('text-sm transition-[color,box-shadow] !bg-amber-500/20')}
                  iconClassName="size-4 text-amber-400 fill-amber-400"
                />
              )}
            </CardAction>
          </CardHeader>
          <CardContent className="flex flex-col gap-4 **:duration-300">
            <CardDescription className="text-secondary-text transition-colors leading-relaxed line-clamp-2">
              {description}
            </CardDescription>
            {showDetails && (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <StarRating rating={rating} />
                  <Badge variant="card">{rating.toFixed(1)}</Badge>
                </div>
                <PriceLevel priceLevel={priceLevel} size="sm" className="py-1" />
              </div>
            )}
            <div className="flex flex-col gap-2.5 *:transition-colors">
              <div className="flex text-accent-text items-center gap-2.5 text-sm">
                <MapPin size={16} className="shrink-0" />
                {address}
              </div>
              <div className="flex text-accent-text/80 items-center gap-2.5 text-sm">
                <Clock size={16} className="shrink-0" />

                {[openHoursString, openingHoursInfo].filter(Boolean).join(' - ') || 'No hours set'}
              </div>
            </div>
          </CardContent>
          <CardFooter className="mt-auto">
            <TagList tags={tags} maxVisible={3} size="sm" />
          </CardFooter>
        </div>
      </div>
    </Card>
  )
}

export default RecommendationCard
