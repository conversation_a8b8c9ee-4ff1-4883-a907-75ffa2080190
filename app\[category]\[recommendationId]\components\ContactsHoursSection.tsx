import { Clock, Globe, MapPin, Navigation, Phone, Mail } from 'lucide-react'
import { cn } from '@/lib/utils'
import IconButton from '@/components/IconButton'
import GlassCard from '@/components/GlassCard'
import { CategoryDetails, Recommendation } from '@/types/types'
import DetailSection from './DetailSection'
import { generateDayHours } from '@/lib/openingHoursHelper'

type Props = {
  recommendation: Recommendation
  iconColor: CategoryDetails['iconColor']
  gradient: CategoryDetails['gradient']
}

const ContactsHoursSection = ({
  gradient,
  iconColor,
  recommendation: { address, openingHours, openingHoursInfo, phone, website, email, googleMaps },
}: Props) => (
  <DetailSection Icon={MapPin} title="Contact & Hours" iconColor={iconColor} animationDelay={300}>
    <GlassCard variant="frostedGlass" className="gap-12">
      <div className="space-y-4 **:transition-colors **:duration-300">
        <div className="flex items-center text-secondary-text gap-3 text-base">
          <MapPin size={16} className="text-accent-text shrink-0" />
          {address}
        </div>
        {(openingHours || openingHoursInfo) && (
          <div
            className={cn('flex gap-3 text-base', openingHours ? 'items-start' : 'items-center')}
          >
            <Clock size={16} className="text-accent-text shrink-0" />
            <div className="flex flex-col flex-1 space-y-4">
              {openingHoursInfo && <span className="text-sm">{openingHoursInfo}</span>}
              {openingHours && (
                <div className="flex-1 space-y-1">
                  {Object.entries(openingHours).map(([day, openHours]) => {
                    if ('periods' in openHours && openHours.periods.length === 0) return null
                    return (
                      <div key={day} className="flex justify-between text-sm">
                        <span className="capitalize font-medium text-secondary-text">{day}:</span>
                        <span className="text-accent-text">
                          {generateDayHours(openHours) || 'Hours not available'}
                        </span>
                      </div>
                    )
                  })}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      {(googleMaps || phone || website || email) && (
        <div className="grid grid-cols-2 gap-3 *:duration-700">
          {googleMaps && (
            <IconButton
              className={cn(
                'bg-gradient-to-r hoverActive:scale-105 text-white border-0 shadow-lg hoverActive:shadow-xl',
                gradient
              )}
              Icon={Navigation}
              text="Directions"
              link={googleMaps}
            />
          )}
          {phone && (
            <IconButton
              variant="frostedGlass"
              className="hoverActive:scale-105"
              Icon={Phone}
              text="Call"
              link={`tel:${phone}`}
            />
          )}
          {website && website !== '#' && (
            <IconButton
              variant="frostedGlass"
              className="hoverActive:scale-105"
              Icon={Globe}
              text="Website"
              link={website}
            />
          )}
          {email && (
            <IconButton
              variant="frostedGlass"
              className="hoverActive:scale-105"
              Icon={Mail}
              text="Email"
              link={`mailto:${email}`}
            />
          )}
        </div>
      )}
    </GlassCard>
  </DetailSection>
)

export default ContactsHoursSection
