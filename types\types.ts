import { DAYS } from '@/data/date'
import { PRICE_LEVEL_MAP, RECOMMENDATION_CATEGORIES, TRAVEL_METHODS } from '@/data/recommendations'
import getScrollConfig from '@/lib/getHeaderScrollConfig'
import { LucideIcon } from 'lucide-react'

export type OpeningHoursData = Partial<Record<WeekDay, DayHours>>

export type Recommendation = {
  id: string
  name: string
  description: string
  rating: number
  priceLevel: PriceLevel
  category: RecommendationCategory
  detailedDescription: string
  images: string[]
  address: string
  coordinates: {
    lat: number
    lng: number
  }
  menuHighlights: string[]
  tags: string[]
  amenities: string[]
  reviews: Review[]
  travelTime: number
  recommendedTravel: TravelMethod | null
  openingHours: OpeningHoursData | null
  openingHoursInfo: string | null
  googleMaps: string | null
  email: string | null
  phone: string | null
  website: string | null
  social: {
    instagram?: string | null
    facebook?: string | null
  } | null
}

export type Review = {
  id: string
  author: string
  rating: number
  comment: string
  date: string
}

export const themes = ['light', 'dark'] as const

export type Theme = (typeof themes)[number]

export const categories = ['eat', 'drink', 'do'] as const

export type Category = (typeof categories)[number]

export type CategoryDetails = {
  id: Category
  name: string
  title: string
  drawerTitle: string
  description: string
  subtitle: string
  gradient: string
  bgGradient: string
  shadowColor: string
  icon: LucideIcon
  accentIcon: LucideIcon
  emoji: string
  iconColor: string
}

export const recommendationListTypes = ['grid', 'list'] as const

export type RecommendationListType = (typeof recommendationListTypes)[number]

export type ScrollConfig = ReturnType<typeof getScrollConfig>

export type FilterState = {
  priceLevel: number[]
  rating: number
  tags: string[]
  amenities: string[]
  travelTime: number
  categories: string[]
}

export type TravelTimeCategory = {
  minutes: number
  text: string
}

export type PriceLevel = keyof typeof PRICE_LEVEL_MAP

export type RecommendationCategory = (typeof RECOMMENDATION_CATEGORIES)[number]

export type TravelMethod = (typeof TRAVEL_METHODS)[number]

export type Coordinates = {
  lng: number
  lat: number
}

export type ImageFile = {
  id: string
  url: string
  name: string
  file?: File
}

export type TimePeriod = {
  openTime: string
  closeTime: string
}

export type DayHours24h = {
  is24Hours: true
}

export type DayHourPeriods = {
  periods: TimePeriod[]
}

export type DayHours = DayHours24h | DayHourPeriods

export type WeekDay = (typeof DAYS)[number]['label']
