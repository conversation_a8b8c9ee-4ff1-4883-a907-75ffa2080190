import { Label } from '@/components/UI/label'
import { AlertTriangle } from 'lucide-react'
import pluralize from 'pluralize'
import React from 'react'

type Props = {
  allErrors: Record<string, string>
}

const OpeningHoursErrors = ({ allErrors }: Props) => (
  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
    <div className="flex items-center gap-2 mb-2">
      <AlertTriangle className="size-4 text-red-600" />
      <Label className="text-sm font-medium text-red-800">
        {pluralize('error', Object.keys(allErrors).length, true)} found
      </Label>
    </div>
    <div className="flex flex-col gap-1">
      {Object.entries(allErrors).map(([day, error], index) => (
        <p key={index} className="text-xs text-red-700">
          <strong className="capitalize">{day}: </strong> {error}.
        </p>
      ))}
    </div>
  </div>
)

export default OpeningHoursErrors
