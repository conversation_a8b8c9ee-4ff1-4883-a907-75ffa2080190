import { FilterState, PriceLevel, Recommendation } from '@/types/types'
import { PRICE_LEVEL_MAP, TRAVEL_TIME_CATEGORIES } from '@/data/recommendations'

export const defaultFilterState: FilterState = {
  priceLevel: [],
  rating: 0,
  tags: [],
  amenities: [],
  travelTime: 0,
  categories: [],
}

/**
 * Get available filter options for a given category
 */
export function getFilterOptions(recommendations: Recommendation[]) {
  const options = recommendations.reduce(
    (acc, rec) => {
      rec.tags.forEach(tag => acc.tags.add(tag))
      rec.amenities.forEach(amenity => acc.amenities.add(amenity))
      acc.categories.add(rec.category)
      acc.priceLevels.add(rec.priceLevel)
      acc.travelTimes.add(getTravelTimeCategory(rec.travelTime).minutes)

      return acc
    },
    {
      tags: new Set<string>(),
      amenities: new Set<string>(),
      categories: new Set<string>(),
      travelTimes: new Set<number>(),
      priceLevels: new Set<PriceLevel>(),
    }
  )

  options.travelTimes.add(0)

  return {
    tags: Array.from(options.tags).sort(),
    amenities: Array.from(options.amenities).sort(),
    categories: Array.from(options.categories).sort(),
    travelTimes: Array.from([...options.travelTimes]).sort((a, b) => a - b),
    priceLevels: Array.from(options.priceLevels).sort(),
    maxRating: 5,
  }
}

/**
 * Apply filters to recommendations
 */
export function applyFilters(
  recommendations: Recommendation[],
  filters: FilterState
): Recommendation[] {
  return recommendations.filter(({ priceLevel, rating, tags, amenities, travelTime, category }) => {
    if (filters.priceLevel.length > 0 && !filters.priceLevel.includes(priceLevel)) {
      return false
    }

    if (filters.rating > 0 && rating < filters.rating) {
      return false
    }

    if (filters.tags.length > 0) {
      const hasMatchingTag = filters.tags.some(tag =>
        tags.some(recTag => recTag.toLowerCase().includes(tag.toLowerCase()))
      )
      if (!hasMatchingTag) return false
    }

    if (filters.amenities.length > 0) {
      const hasMatchingAmenity = filters.amenities.some(amenity =>
        amenities.some(recAmenity => recAmenity.toLowerCase().includes(amenity.toLowerCase()))
      )
      if (!hasMatchingAmenity) return false
    }

    if (filters.categories.length > 0 && !filters.categories.includes(category)) {
      return false
    }

    if (filters.travelTime && travelTime && travelTime > filters.travelTime) {
      return false
    }

    return true
  })
}

/**
 * Count active filters
 */
export function getActiveFiltersCount(filters: FilterState): number {
  const activeFilters = [
    filters.priceLevel.length > 0,
    filters.rating > 0,
    filters.tags.length > 0,
    filters.amenities.length > 0,
    filters.travelTime > 0,
    filters.categories.length > 0,
  ]

  return activeFilters.filter(Boolean).length
}

/**
 * Get price level display text
 */
export function getPriceLevelText(level: keyof typeof PRICE_LEVEL_MAP): string {
  return PRICE_LEVEL_MAP[level] ?? `$${'$'.repeat(Math.max(0, level - 1))}`
}

/**
 * Get travel time category
 */
export function getTravelTimeCategory(minutes: number) {
  const category =
    TRAVEL_TIME_CATEGORIES.find(cat => minutes <= cat.maxMinutes) ??
    TRAVEL_TIME_CATEGORIES[TRAVEL_TIME_CATEGORIES.length - 1]

  return { minutes: category.maxMinutes, text: category.text }
}

export function getNumberFromText(text: string) {
  const minutes = parseInt(text.match(/\d+/)?.[0] || '0')
  return minutes
}
