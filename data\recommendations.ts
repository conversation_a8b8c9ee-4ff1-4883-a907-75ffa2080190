import { Recommendation } from '@/types/types'
import { DEFAULT_PLACE } from './mockData'

export const PRICE_LEVEL_MAP: Record<number, string> = {
  0: 'Free',
  1: 'Budget ($)',
  2: 'Moderate ($$)',
  3: 'Premium ($$$)',
}

export const TRAVEL_TIME_CATEGORIES = [
  { maxMinutes: 0, text: 'All' },
  { maxMinutes: 5, text: 'Very Close (≤5 min)' },
  { maxMinutes: 10, text: 'Close (≤10 min)' },
  { maxMinutes: 15, text: 'Moderate (≤15 min)' },
] as const

export const RECOMMENDATION_CATEGORIES = [
  'Café',
  'Bakery',
  'Restaurant',
  'Beer Bar',
  'Wine Bar',
  'Museum',
  'Park',
  'Gallery',
  'Shop',
  'Outdoor Activity',
  'Cultural Tour',
  'Religious Site',
] as const

export const TRAVEL_METHODS = ['walking', 'biking', 'public_transport', 'car'] as const

export const defaultRecommendation = {
  name: '',
  description: '',
  detailedDescription: '',
  category: 'Café' as Recommendation['category'],
  rating: 4.0,
  priceLevel: 1,
  tags: [],
  amenities: [],
  images: [],
  menuHighlights: [],
  address: DEFAULT_PLACE.address,
  coordinates: DEFAULT_PLACE.coordinates,
}
