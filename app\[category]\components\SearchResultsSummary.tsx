import pluralize from 'pluralize'

const getAdjustmentSuggestion = (searchQuery: string, activeFiltersCount: number) => {
  const suggestion = 'Try adjusting your'

  if (searchQuery && activeFiltersCount > 0) {
    return suggestion + ' search or filters.'
  }
  if (searchQuery) {
    return suggestion + ' search terms.'
  }
  if (activeFiltersCount > 0) {
    return suggestion + ' filters.'
  }

  return suggestion + ' search or filters.'
}

type Props = {
  numberOfRecommendations: number
  searchQuery: string
  activeFiltersCount: number
}

const SearchResultsSummary = ({
  numberOfRecommendations,
  searchQuery,
  activeFiltersCount,
}: Props) => (
  <>
    <p className="flex gap-2 items-center text-primary-text font-medium text-sm">
      {numberOfRecommendations === 0
        ? 'No place match your criteria.'
        : `${pluralize('place', numberOfRecommendations, true)} found!`}
    </p>
    {numberOfRecommendations === 0 && (
      <p className="text-sm text-secondary-text">
        {getAdjustmentSuggestion(searchQuery, activeFiltersCount)}
      </p>
    )}
  </>
)

export default SearchResultsSummary
