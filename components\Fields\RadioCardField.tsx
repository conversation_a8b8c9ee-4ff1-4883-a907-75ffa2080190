import RadioCard from '@/components/RadioCard'
import { FormControl, FormField, FormItem } from '@/components/UI/form'
import { RadioGroup } from '@/components/UI/radio-group'
import { Control, FieldValues, Path } from 'react-hook-form'

type Props<T extends FieldValues, U extends string | number> = {
  control: Control<T>
  options: U[]
  name: Path<T>
  gradient: string
  onValueChange?: (value: string) => void
  labelFormatter?: (label: string) => string
}

const RadioCardField = <T extends FieldValues, U extends string | number>({
  control,
  options,
  name,
  gradient,
  onValueChange,
  labelFormatter,
}: Props<T, U>) => (
  <FormField
    control={control}
    name={name}
    render={({ field: { value, onChange } }) => (
      <FormItem>
        <FormControl>
          <RadioGroup
            value={value.toString()}
            onValueChange={val => {
              if (onValueChange) {
                onValueChange(val)
              } else {
                onChange(val)
              }
            }}
            className="grid sm:grid-cols-2 gap-2"
          >
            {options.map(val => {
              const stringVal = val.toString()
              const active = value.toString() == stringVal
              return (
                <RadioCard
                  key={stringVal}
                  value={stringVal}
                  label={labelFormatter ? labelFormatter(stringVal) : stringVal}
                  active={active}
                  gradient={gradient}
                />
              )
            })}
          </RadioGroup>
        </FormControl>
      </FormItem>
    )}
  />
)

export default RadioCardField
