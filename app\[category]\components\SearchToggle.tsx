'use client'

import { useState, useRef, useEffect, ChangeEvent, FormEvent } from 'react'
import { Search, X } from 'lucide-react'
import { Input } from '@/components/UI/input'
import { cn } from '@/lib/utils'
import IconButton from '@/components/IconButton'
import { Category } from '@/types/types'
import { queryFiltersSearchParams } from '@/lib/searchParams'
import { useQueryStates } from 'nuqs'

type Props = {
  category: Category
}

const SearchToggle = ({ category }: Props) => {
  const [{ q: searchParamsQuery }, setQueryFilters] = useQueryStates(queryFiltersSearchParams, {
    history: 'replace',
    shallow: false,
  })
  const [searchQuery, setSearchQuery] = useState(searchParamsQuery.trim() || '')
  const [isSearchActive, setIsSearchActive] = useState(!!searchParamsQuery.trim())
  const [isFocused, setIsFocused] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (isSearchActive && inputRef.current) inputRef.current?.focus()
  }, [isSearchActive])

  useEffect(() => {
    if (!!searchQuery.trim() && !isSearchActive) {
      setIsSearchActive(true)
    }
  }, [searchQuery, isSearchActive])

  useEffect(() => {
    if (!searchParamsQuery.trim()) {
      setSearchQuery('')
      setIsSearchActive(false)
    }
  }, [searchParamsQuery])

  const handleSearchClick = () => {
    if (!isSearchActive) {
      setIsSearchActive(true)
    }
  }

  const handleClearSearch = () => setQueryFilters({ q: '' })
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!!searchQuery.trim() && searchQuery !== searchParamsQuery) {
      setIsSearchActive(false)
      setQueryFilters({ q: searchQuery })
    }
  }

  const handleInputBlur = () => {
    setIsFocused(false)
    if (!searchQuery.trim()) {
      setIsSearchActive(false)
    }
  }

  const handleInputFocus = () => setIsFocused(true)

  return (
    <div className="relative">
      {!isSearchActive ? (
        <IconButton
          size="sm"
          variant="frostedGlass"
          text="Search"
          Icon={Search}
          onClick={handleSearchClick}
          className="hoverActive:scale-105"
        />
      ) : (
        <div className={cn('relative', isSearchActive && 'w-64')}>
          <form
            onSubmit={handleSubmit}
            className={cn(
              'relative bg-white/30 backdrop-blur-xl rounded-full shadow-lg transition-all',
              isFocused && 'shadow-xl scale-102 ring-2 ring-purple-400/50'
            )}
          >
            <IconButton
              Icon={Search}
              variant="ghost"
              size="sm"
              type="submit"
              iconClassName={isFocused ? '!text-accent-text' : '!text-secondary-text'}
              className={cn(
                'transition-colors absolute left-2 top-1/2 transform -translate-y-1/2 size-8 p-0 rounded-full hoverActive:!bg-white/30 hoverActive:scale-110'
              )}
            />
            <Input
              ref={inputRef}
              value={searchQuery}
              name="searchQuery"
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              placeholder={`Search ${category} places...`}
              className="px-12 py-3 text-primary-text placeholder:text-secondary-text/60 !border-0 !ring-0 !outline-0 text-base rounded-full"
            />
            {searchQuery && (
              <IconButton
                Icon={X}
                variant="ghost"
                size="sm"
                onClick={handleClearSearch}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 size-8 p-0 rounded-full hoverActive:!bg-white/30 hoverActive:scale-110"
              />
            )}
            <div
              className={cn(
                'absolute inset-0 rounded-full transition-all pointer-events-none',
                isFocused && 'bg-gradient-to-t from-transparent via-white/5 to-white/15'
              )}
            />
          </form>
        </div>
      )}
    </div>
  )
}

export default SearchToggle
