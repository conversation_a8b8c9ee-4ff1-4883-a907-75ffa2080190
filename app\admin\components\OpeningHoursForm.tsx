'use client'

import { useEffect } from 'react'
import { Card, CardContent } from '@/components/UI/card'
import { RotateCcw, Save, X } from 'lucide-react'
import IconButton from '@/components/IconButton'
import { DAYS, WEEKDAYS } from '@/data/date'
import { OpeningHoursData, WeekDay } from '@/types/types'
import { cn } from '@/lib/utils'
import { checkPeriodsOverlap, validatePeriodTimes } from '@/lib/openingHoursHelper'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import OpeningHoursPreview from './OpeningHoursPreview'
import { Form } from '@/components/UI/form'
import isEmpty from 'lodash/isEmpty'
import OpeningHoursErrors from './OpeningHoursErrors'
import OpeningHoursPresets from './OpeningHoursPresets'
import OpeningHourDayHeader from './OpeningHourDayHeader'
import OpeningHourDayBody from './OpeningHourDayBody'

const refineOpeningHours = (data: OpeningHoursData, ctx: z.RefinementCtx) => {
  Object.entries(data).forEach(([weekDay, dayHours]) => {
    if ('is24Hours' in dayHours || ('periods' in dayHours && dayHours.periods.length === 0)) return

    dayHours.periods.forEach((period, index) => {
      const timeValidation = validatePeriodTimes(period.openTime, period.closeTime)
      if (!timeValidation.isValid) {
        ctx.addIssue({
          code: 'custom',
          message: timeValidation.message,
          path: [weekDay, 'periods', index, 'closeTime'],
          input: period.closeTime,
        })
      }
    })

    const overlapCheck = checkPeriodsOverlap(dayHours.periods)
    if (overlapCheck.hasOverlap) {
      ctx.addIssue({
        code: 'custom',
        message: overlapCheck.message,
        path: [weekDay, 'periods'],
        input: dayHours.periods,
      })
    }
  })
}

const dayHoursSchema = z.object({
  is24Hours: z.boolean(),
  periods: z.array(
    z.object({
      openTime: z.string(),
      closeTime: z.string(),
    })
  ),
})

export type DayHoursSchema = z.infer<typeof dayHoursSchema>

const weekSchemaShape: { [K in WeekDay]: typeof dayHoursSchema } = WEEKDAYS.reduce(
  (acc, day) => {
    acc[day] = dayHoursSchema
    return acc
  },
  {} as { [K in WeekDay]: typeof dayHoursSchema }
)

const schema = z.object(weekSchemaShape).superRefine(refineOpeningHours)

export type OpeningHoursSchema = z.infer<typeof schema>

export const generateInitialHours = (openHours: OpeningHoursData) =>
  Object.fromEntries(
    DAYS.map(day => {
      const dayData = openHours[day.label]
      if (!dayData) return [day.label, { periods: [] }]

      const dayHours = {
        is24Hours: 'is24Hours' in dayData,
        periods: 'periods' in dayData ? dayData.periods : [],
      }

      return [day.label, dayHours]
    })
  )

type Props = {
  openHours: OpeningHoursData
  onSave: (openHours: OpeningHoursData) => void
  onCancel: () => void
}

const OpeningHoursForm = ({ openHours, onSave, onCancel }: Props) => {
  const form = useForm<OpeningHoursSchema>({
    resolver: zodResolver(schema),
    defaultValues: generateInitialHours(openHours),
    mode: 'all',
  })

  const {
    reset,
    setValue,
    formState: { errors },
    watch,
  } = form

  const days = watch()

  useEffect(() => {
    reset(generateInitialHours(openHours))
  }, [openHours, reset])

  const updateDayHours = (day: keyof OpeningHoursData, dayHours: DayHoursSchema) => {
    setValue(day, dayHours, { shouldValidate: true })
  }

  const getAllErrors = () => {
    const allErrorsObject: Record<string, string> = {}
    Object.entries(errors).forEach(([day, dayErrors]) => {
      allErrorsObject[day] = ''
      if (dayErrors?.periods) {
        if (Array.isArray(dayErrors.periods)) {
          dayErrors.periods.forEach(periodErrors => {
            if (periodErrors?.closeTime) {
              allErrorsObject[day] += periodErrors.closeTime.message + ' '
            }
          })
        } else if (dayErrors.periods.message) {
          allErrorsObject[day] += dayErrors.periods.message + ' '
        }
      }
    })
    return allErrorsObject
  }

  async function handleClickSubmit() {
    const result = await form.trigger()
    console.log('result', result)
    if (result) onSave(form.getValues())
  }

  const allErrors = getAllErrors()
  const hasErrors = !isEmpty(allErrors)

  return (
    <Form {...form}>
      <div className="flex flex-col gap-4 divide-y *:not-last:pb-4">
        {hasErrors && <OpeningHoursErrors allErrors={allErrors} />}
        <OpeningHoursPresets reset={reset} />
        <div className="flex flex-col gap-4">
          {(Object.entries(days) as [WeekDay, DayHoursSchema][]).map(([day, dayHours], index) => (
            <Card
              key={day}
              className={cn(
                'bg-gray-50 border-gray-200 py-0 shadow-xs',
                allErrors[day] && 'border-red-300'
              )}
            >
              <CardContent className="flex flex-col gap-2 p-3">
                <OpeningHourDayHeader
                  day={day}
                  days={days}
                  dayIndex={index}
                  dayHours={dayHours}
                  updateDayHours={updateDayHours}
                  error={allErrors[day]}
                />
                <OpeningHourDayBody
                  day={day}
                  days={days}
                  dayIndex={index}
                  dayHours={dayHours}
                  updateDayHours={updateDayHours}
                  errors={errors}
                />
              </CardContent>
            </Card>
          ))}
        </div>

        <OpeningHoursPreview openHours={days} hasErrors={hasErrors} />
        <div className="flex flex-row justify-between items-center flex-wrap gap-2">
          <IconButton
            Icon={RotateCcw}
            text="Reset"
            size="sm"
            variant="ghost"
            onClick={() => reset(generateInitialHours(openHours))}
            className="rounded-lg"
          />
          <div className="flex gap-2 flex-wrap">
            <IconButton
              Icon={X}
              size="sm"
              text="Cancel"
              variant="outline"
              onClick={onCancel}
              className="rounded-lg"
            />
            <IconButton
              Icon={Save}
              size="sm"
              text="Save"
              variant="active"
              className="rounded-lg"
              onClick={handleClickSubmit}
              disabled={hasErrors}
            />
          </div>
        </div>
      </div>
    </Form>
  )
}

export default OpeningHoursForm
