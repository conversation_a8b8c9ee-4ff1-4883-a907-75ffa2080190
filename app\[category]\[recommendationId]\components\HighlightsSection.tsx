import GlassCard from '@/components/GlassCard'
import { cn } from '@/lib/utils'
import { CategoryDetails, Recommendation } from '@/types/types'
import { Sparkles } from 'lucide-react'
import DetailSection from './DetailSection'

type Props = {
  gradient: CategoryDetails['gradient']
  iconColor: CategoryDetails['iconColor']
  menuHighlights: Recommendation['menuHighlights']
}

const Highlights = ({ gradient, iconColor, menuHighlights }: Props) => (
  <DetailSection Icon={Sparkles} title="Highlights" iconColor={iconColor} animationDelay={150}>
    <GlassCard variant="frostedGlass">
      <ul className="space-y-3 text-base **:transition-colors **:duration-300">
        {menuHighlights.map((highlight, index) => (
          <li
            key={index}
            className="flex items-center gap-3 text-secondary-text hoverActive:text-primary-text p-2"
          >
            <div
              className={cn('size-2 bg-gradient-to-r rounded-full shadow-lg shrink-0', gradient)}
            />
            {highlight}
          </li>
        ))}
      </ul>
    </GlassCard>
  </DetailSection>
)

export default Highlights
