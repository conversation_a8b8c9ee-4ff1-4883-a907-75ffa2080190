'use client'

import { ChangeEvent, useRef } from 'react'
import { toast } from 'sonner'
import IconButton from './IconButton'
import { Upload } from 'lucide-react'

type Props<T> = {
  onImport: (data: T) => void
}

const ImportButton = <T,>({ onImport }: Props<T>) => {
  const importRef = useRef<HTMLInputElement>(null)

  const handleImportData = (data: ChangeEvent<HTMLInputElement>) => {
    if (!data || !data.target?.files?.length) {
      toast.error('No file selected')
      return
    }
    const file = data.target.files[0]
    if (file.type !== 'application/json') {
      toast.error('Please upload a valid JSON file')
      return
    }
    const reader = new FileReader()
    reader.onload = e => {
      try {
        const json = JSON.parse(e.target?.result as string)
        if (!json) {
          toast.error('Invalid JSON format')
          return
        }
        onImport(json)
        if (importRef.current) importRef.current.value = ''
        toast.success('Imported recommendations successfully')
      } catch (error) {
        console.error('Import error:', error)
        toast.error('Failed to import recommendations')
      }
    }
    reader.readAsText(file)
  }

  return (
    <>
      <input
        ref={importRef}
        type="file"
        accept=".json"
        onChange={handleImportData}
        className="hidden"
      />
      <IconButton
        variant="outline"
        size="sm"
        Icon={Upload}
        text="Import"
        onClick={() => importRef.current?.click()}
        className="rounded-lg"
      />
    </>
  )
}

export default ImportButton
