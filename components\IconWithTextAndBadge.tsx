import { LucideIcon } from 'lucide-react'
import { Badge } from '@/components/UI/badge'

type Props = {
  Icon: LucideIcon
  text?: string
  badgeText?: string | number
  iconClassName?: string
  size?: number
}

const IconWithTextAndBadge = ({ Icon, text, badgeText, iconClassName, size = 16 }: Props) => (
  <>
    <Icon size={size} className={iconClassName} />
    {text && text}
    {badgeText && (
      <Badge variant="card" className="rounded-md border-glass-border" size="sm">
        {badgeText}
      </Badge>
    )}
  </>
)

export default IconWithTextAndBadge
