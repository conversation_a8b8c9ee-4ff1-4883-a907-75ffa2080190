import GlassCard from '@/components/GlassCard'
import PriceLevel from '@/components/PriceLevel'
import RecommendationImageOverlay from '@/components/RecommendationImageOverlay'
import StarRating from '@/components/StarRating'
import TagList from '@/components/BadgeTagList'
import { CardHeader, CardTitle, CardDescription } from '@/components/UI/card'
import { getCategoryDetailsById } from '@/lib/categoryHelper'
import { getTravelInfoDetails } from '@/lib/recommendationHelper'
import { Category, Recommendation } from '@/types/types'
import { Badge } from '@/components/UI/badge'
import RecommendationImageCarousel from './RecommendationImageCarousel'

type Props = {
  recommendation: Recommendation
  categoryId: Category
}

const MainInformationSection = ({
  categoryId,
  recommendation,
  recommendation: {
    name,
    description,
    rating,
    priceLevel,
    images,
    tags,
    reviews,
    travelTime,
    recommendedTravel,
  },
}: Props) => {
  const { icon, gradient } = getCategoryDetailsById(categoryId)
  const { text: travelInfoText, icon: TravelIcon } =
    getTravelInfoDetails(travelTime, recommendedTravel) || {}

  const imageOverlay = (
    <RecommendationImageOverlay icon={icon} gradient={gradient} recommendation={recommendation} />
  )

  return (
    <section className="flex flex-col gap-6">
      <RecommendationImageCarousel images={images} name={name} overlay={imageOverlay} />
      <div className="flex flex-col gap-6 px-6 animate-fade-up animation-duration-1000 **:transition-colors **:duration-300">
        <h1 className="text-3xl font-bold">{name}</h1>
        <p className="text-secondary-text text-lg leading-relaxed">{description}</p>
        {travelInfoText && (
          <GlassCard className="flex-row items-center gap-3">
            {TravelIcon && <TravelIcon size={16} />}
            <CardHeader className="flex-1 px-0">
              <CardTitle className="text-sm font-medium text-primary-text">
                {travelInfoText}
              </CardTitle>
              <CardDescription className="text-xs text-accent-text">
                Recommended travel method
              </CardDescription>
            </CardHeader>
          </GlassCard>
        )}
        <GlassCard className="flex-row items-center justify-between gap-1">
          <div className="flex justify-between gap-3 flex-wrap-reverse">
            <StarRating rating={rating} />
            <Badge variant="card" className="!bg-amber-500/20 backdrop-blur-sm gap-2 !shadow-none">
              <span className="text-sm text-amber-700 dark:text-amber-100">
                {rating.toFixed(1)}
              </span>
              <span className="font-normal text-amber-600 dark:text-amber-200">
                ({reviews.length} reviews)
              </span>
            </Badge>
          </div>
          <PriceLevel priceLevel={priceLevel} />
        </GlassCard>
        <TagList tags={tags} border />
      </div>
    </section>
  )
}

export default MainInformationSection
