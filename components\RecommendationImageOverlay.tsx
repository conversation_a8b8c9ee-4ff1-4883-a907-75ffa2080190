import { Star } from 'lucide-react'
import { cn } from '@/lib/utils'
import IconBadge from '@/components/IconBadge'
import { getTravelInfoDetails } from '@/lib/recommendationHelper'
import { CategoryDetails, Recommendation } from '@/types/types'
import LikeButton from '@/components/LikeButton'
import EditRecommendationButton from '@/app/admin/components/EditRecommendationButton'
import DeleteRecommendationButton from '@/app/admin/components/DeleteRecommendationButton'

type Props = {
  recommendation: Recommendation
  icon: CategoryDetails['icon']
  gradient?: CategoryDetails['gradient']
  showLikeButton?: boolean
  showActionButton?: boolean
}

const RecommendationImageOverlay = ({
  recommendation,
  recommendation: { id, rating, category, travelTime, recommendedTravel },
  gradient,
  icon: CategoryIcon,
  showLikeButton = false,
  showActionButton = false,
}: Props) => {
  const { text: travelInfoText, icon: TravelIcon } =
    getTravelInfoDetails(travelTime, recommendedTravel) || {}

  return (
    <>
      <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-black/10 to-transparent group-hoverActive:from-black/50 transition-colors duration-500 pointer-events-none" />
      <div className="*:absolute *:backdrop-blur-md *:text-sm **:duration-300">
        <IconBadge
          text={category}
          Icon={CategoryIcon}
          variant="card"
          className={cn('top-3 left-3 bg-gradient-to-br text-white border-0', gradient)}
        />
        {travelInfoText && TravelIcon && (
          <IconBadge
            text={travelInfoText}
            Icon={TravelIcon}
            variant="card"
            className="top-3 right-3"
          />
        )}
        {showActionButton && id && (
          <div className="absolute bottom-3 right-3 flex items-center gap-2 !backdrop-blur-none">
            <EditRecommendationButton recommendation={recommendation} />
            <DeleteRecommendationButton id={id} />
          </div>
        )}
        {!showActionButton && showLikeButton && id && (
          <LikeButton recommendationId={id} className="bottom-3 right-3 border-transparent" />
        )}
        <IconBadge
          text={rating.toString()}
          Icon={Star}
          variant="card"
          className={cn('bottom-3', showLikeButton ? 'left-3' : 'right-3')}
          iconClassName="text-amber-400 fill-amber-400"
        />
      </div>
    </>
  )
}

export default RecommendationImageOverlay
