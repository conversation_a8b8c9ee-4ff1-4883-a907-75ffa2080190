import { Button } from '@/components/UI/button'
import { PRESETS } from '@/data/date'
import { OpeningHoursData } from '@/types/types'

type Props = {
  reset: (preset: OpeningHoursData) => void
}

const OpeningHoursPresets = ({ reset }: Props) => (
  <div className="flex flex-col gap-2">
    <h3 className="text-sm font-medium">Quick Presets</h3>
    <div className="flex flex-wrap gap-1">
      {Object.keys(PRESETS).map(presetName => (
        <Button
          key={presetName}
          type="button"
          variant="outline"
          size="sm"
          onClick={() => {
            const preset = PRESETS[presetName]
            if (preset) reset(preset)
          }}
          className="text-xs px-2 py-0.5 h-auto"
        >
          {presetName}
        </Button>
      ))}
    </div>
  </div>
)

export default OpeningHoursPresets
