import { cn } from '@/lib/utils'
import { RadioGroupItem } from '@/components/UI/radio-group'

type Props = {
  value: string
  label: string
  active: boolean
  gradient: string
}

const RadioCard = ({ value, label, gradient, active }: Props) => (
  <label
    className={cn(
      'flex items-center gap-3 p-3 rounded-2xl border-[1px] border-glass-border transition-everything duration-300 cursor-pointer hoverActive:scale-102 shadow-sm hoverActive:shadow-md',
      active
        ? 'bg-gradient-to-r border-x-0 shadow-lg text-white ' + gradient
        : 'bg-card-bg hoverActive:bg-card-hover text-primary-text'
    )}
  >
    <RadioGroupItem value={value} id={value} className="duration-300" />
    <span className="text-sm font-medium">{label}</span>
  </label>
)

export default RadioCard
