import { Car, Navigation } from 'lucide-react'
import { categories, Category, PriceLevel, Recommendation } from '@/types/types'
import times from 'lodash/times'
import { MAPBOX_CONFIG } from '@/lib/mapConfig'
import { mockRecommendations } from '@/data/mockData'

export const getStarRating = (rating: number): { filled: 'full' | 'half' | 'none' }[] =>
  times(5, i => ({
    filled: rating >= i + 1 ? 'full' : rating >= i + 0.5 ? 'half' : 'none',
  }))

export const getPriceLevels = (priceLevel: PriceLevel) =>
  times(4, i => ({ filled: i < priceLevel }))

export const getOpenStatus = (openingHours: Recommendation['openingHours']) => {
  if (!openingHours) return false
  const now = new Date()
  const currentDay = now.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase()
  const todaySchedule = Object.entries(openingHours).find(([day]) => day.includes(currentDay))

  if (todaySchedule && todaySchedule[1].periods?.length) {
    const hours = todaySchedule[1]
    if (hours.is24Hours) return true

    const time = now.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
    })
    const [currentHour, currentMinute] = time.split(':')

    for (const period of hours.periods) {
      const [start, end] = [period.openTime, period.closeTime]
      const [startHour, startMinute] = start.split(':')
      const [endHour, endMinute] = end.split(':')

      const isAfterStart =
        Number(currentHour) > Number(startHour) ||
        (Number(currentHour) === Number(startHour) && Number(currentMinute) >= Number(startMinute))
      const isBeforeEnd =
        Number(currentHour) < Number(endHour) ||
        (Number(currentHour) === Number(endHour) && Number(currentMinute) <= Number(endMinute))

      if (isAfterStart && isBeforeEnd) return true
    }
  } else {
    return false
  }

  return false
}

export const getTravelInfoDetails = (
  travelTime: Recommendation['travelTime'],
  recommendedTravel: Recommendation['recommendedTravel']
) => {
  if (!travelTime || !recommendedTravel) {
    return null
  }

  return {
    icon: recommendedTravel === 'car' ? Car : Navigation,
    text: `${travelTime} minutes by ${recommendedTravel}`,
    method: recommendedTravel,
  }
}

export const getReviewerName = (reviewer: string | undefined | null) => {
  if (!reviewer || typeof reviewer !== 'string') {
    return 'Anonymous User'
  }
  return reviewer
}

export const reverseGeocode = async (lng: number, lat: number): Promise<string> => {
  try {
    const response = await fetch(
      `https://api.mapbox.com/search/geocode/v6/reverse?longitude=${lng}&latitude=${lat}&access_token=${MAPBOX_CONFIG.accessToken}&country=hu&limit=1`
    )
    const data = await response.json()

    if (data.features && data.features.length > 0) {
      return data.features[0].properties.full_address
    }
    return ''
  } catch (err) {
    console.error('Reverse geocoding failed:', err)
    return ''
  }
}

export const generateGoogleMapsLink = (lat: number, lng: number, address?: string) => {
  const baseUrl = 'https://www.google.com/maps'

  if (address) {
    return `${baseUrl}/search/?api=1&query=${encodeURIComponent(address)}`
  } else {
    return `${baseUrl}/@${lat},${lng},17z`
  }
}

export function getRecommendationsWithTravelInfo(): Record<Category, Recommendation[]>
export function getRecommendationsWithTravelInfo(category: Category): Recommendation[]
export function getRecommendationsWithTravelInfo(
  category?: Category
): Record<Category, Recommendation[]> | Recommendation[] {
  if (!category) {
    const result: Record<Category, Recommendation[]> = { eat: [], drink: [], do: [] }
    for (const cat of categories) {
      const travelInfoRecommendations = mockRecommendations[cat].map(recom => recom)
      result[cat] = travelInfoRecommendations
    }
    return result
  }

  return mockRecommendations[category].map(recom => recom)
}
