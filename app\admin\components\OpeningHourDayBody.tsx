import { DayHours, OpeningHoursData, TimePeriod } from '@/types/types'
import React from 'react'
import OpeningHourPeriodActionButtons from './OpeningHourPeriodActionButtons'
import { AlertTriangle, Moon, Sun } from 'lucide-react'
import OpeningHourInput from './OpeningHourInput'
import { cn } from '@/lib/utils'
import { DayHoursSchema, OpeningHoursSchema } from './OpeningHoursForm'
import { FieldErrors } from 'react-hook-form'

type Props = {
  day: keyof OpeningHoursData
  days: OpeningHoursSchema
  dayHours: DayHours
  dayIndex: number
  updateDayHours: (day: keyof OpeningHoursData, dayHours: DayHoursSchema) => void
  errors: FieldErrors<OpeningHoursData>
}

const OpeningHourDayBody = ({ day, days, dayHours, dayIndex, errors, updateDayHours }: Props) => {
  if (('periods' in dayHours && dayHours.periods.length === 0) || 'is24Hours' in dayHours){
    return null}

  const addPeriod = () => {
    const newPeriod: TimePeriod = {
      openTime: '09:00',
      closeTime: '17:00',
    }

    updateDayHours(day, {
      is24Hours: false,
      periods: [...days[day].periods, newPeriod],
    })
  }

  const removePeriod = (idx: number) => {
    updateDayHours(day, {
      ...days[day],
      periods: days[day].periods.filter((_, i) => i !== idx),
    })
  }

  const updatePeriod = (idx: number, field: 'openTime' | 'closeTime', val: string) => {
    updateDayHours(day, {
      ...days[day],
      periods: days[day].periods.map((p, i) => (i === idx ? { ...p, [field]: val } : p)),
    })
  }

  return (
    <div className="flex flex-col gap-2">
      {dayHours.periods?.map((period, periodIndex) => {
        const periodError =
          errors[day]?.periods?.[periodIndex]?.closeTime?.message || errors[day]?.periods?.message

        return (
          <div
            key={`${dayIndex}-${periodIndex}`}
            className={cn(
              'flex flex-col gap-2 bg-white p-2 rounded border',
              periodError ? 'border-red-300 bg-red-50' : 'border-gray-200'
            )}
          >
            <OpeningHourPeriodActionButtons
              periodIndex={periodIndex}
              periodsLength={dayHours.periods.length}
              onAdd={addPeriod}
              onRemove={() => removePeriod(periodIndex)}
              error={periodError}
            />

            <div className="grid grid-cols-2 gap-2">
              <OpeningHourInput
                label="Open"
                labelIcon={Sun}
                labelIconColor="text-yellow-500"
                name={`${dayIndex}-${periodIndex}-open`}
                value={period.openTime}
                onChange={time => updatePeriod(periodIndex, 'openTime', time)}
                error={periodError}
              />
              <OpeningHourInput
                label="Close"
                labelIcon={Moon}
                labelIconColor="text-indigo-500"
                name={`${dayIndex}-${periodIndex}-close`}
                value={period.closeTime}
                onChange={time => updatePeriod(periodIndex, 'closeTime', time)}
                error={periodError}
              />
            </div>

            {periodError && (
              <p className="flex items-center gap-2 text-red-600 text-xs">
                <AlertTriangle size={12} /> {periodError}
              </p>
            )}
          </div>
        )
      })}
    </div>
  )
}

export default OpeningHourDayBody
