import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'

type Props = {
  gradient: string
  icon: LucideIcon
  text?: string
}

const CircleIconWithText = ({ gradient, icon: Icon, text }: Props) => (
  <div className="flex text-base font-normal items-center gap-3">
    <div
      className={cn(
        'size-10 rounded-full bg-gradient-to-r flex items-center justify-center',
        gradient
      )}
    >
      <Icon size={20} color="white" />
    </div>
    {text && text}
  </div>
)

export default CircleIconWithText
