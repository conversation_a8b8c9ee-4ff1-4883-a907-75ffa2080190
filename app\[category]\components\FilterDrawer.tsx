'use client'

import { useEffect, useMemo } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryStates } from 'nuqs'
import isEqual from 'lodash/isEqual'
import {
  Drawer,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
} from '@/components/UI/drawer'
import { Form } from '@/components/UI/form'
import IconButton from '@/components/IconButton'
import DrawerSection from './DrawerSection'
import { X, Filter, RotateCcw } from 'lucide-react'
import {
  getFilterOptions,
  getPriceLevelText,
  getActiveFiltersCount,
  defaultFilterState,
  getTravelTimeCategory,
} from '@/lib/filterUtils'
import { getCategoryDetailsById } from '@/lib/categoryHelper'
import { useFilterDrawer } from '@/stores/filterDrawer.store'
import { useShallow } from 'zustand/react/shallow'
import { Category, Recommendation } from '@/types/types'
import { cn } from '@/lib/utils'
import pluralize from 'pluralize'
import { queryFiltersSearchParams, filterSchema, FilterFormType } from '@/lib/searchParams'
import CheckboxCardField from '@/components/Fields/CheckboxCardField'
import SliderField from '@/components/Fields/SliderField'
import RadioCardField from '@/components/Fields/RadioCardField'
import CircleIconWithText from '@/components/CircleIconWithText'

type Props = {
  category: Category
  recommendations: Recommendation[]
}

const FilterDrawer = ({ recommendations, category }: Props) => {
  const { isFilterDrawerOpen, setIsFilterDrawerOpen } = useFilterDrawer(
    useShallow(state => ({
      isFilterDrawerOpen: state.isFilterDrawerOpen,
      setIsFilterDrawerOpen: state.setIsFilterDrawerOpen,
    }))
  )
  const filterOptions = getFilterOptions(recommendations)
  const { gradient, drawerTitle } = getCategoryDetailsById(category)
  const [queryFilters, setQueryFilters] = useQueryStates(queryFiltersSearchParams, {
    history: 'replace',
    shallow: false,
  })

  const form = useForm<FilterFormType>({
    resolver: zodResolver(filterSchema),
    defaultValues: queryFilters,
  })

  const { handleSubmit, watch, reset, control, setValue } = form
  const filters = watch()
  const activeFiltersCount = getActiveFiltersCount(filters)

  const handleClose = () => setIsFilterDrawerOpen(false)
  const handleClickResetAll = () => reset(defaultFilterState)
  const handleClickResetLocal = () => reset(queryFilters)

  const handleTravelTimeChange = (value: string) => {
    setValue('travelTime', parseInt(value), { shouldValidate: true })
  }

  const onSubmit: SubmitHandler<FilterFormType> = data => {
    setQueryFilters(data)
    setIsFilterDrawerOpen(false)
  }

  useEffect(() => {
    if (isFilterDrawerOpen) reset(queryFilters)
  }, [queryFilters, isFilterDrawerOpen, reset])

  const travelTimeBadgeText = useMemo(
    () => getTravelTimeCategory(filters.travelTime).text,
    [filters.travelTime]
  )

  return (
    <Drawer open={isFilterDrawerOpen} onClose={handleClose}>
      <DrawerContent
        className="max-h-[85vh] bg-card-bg backdrop-blur-xl border border-muted-foreground/40"
        thumbClassName="bg-secondary-text"
      >
        <DrawerHeader className="flex-row items-center justify-between bg-card-bg backdrop-blur-xl border-b border-muted-foreground/40 sticky top-0 z-10 mt-4">
          <div className="flex items-center gap-3 text-left">
            <CircleIconWithText gradient={gradient} icon={Filter} />
            <div>
              <DrawerTitle className="text-primary-text text-lg">{drawerTitle}</DrawerTitle>
              <DrawerDescription className="text-secondary-text">
                {activeFiltersCount > 0
                  ? `${pluralize('filter', activeFiltersCount, true)} active`
                  : 'Customize your search'}
              </DrawerDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {!isEqual(filters, queryFilters) && (
              <IconButton
                Icon={RotateCcw}
                text="Reset"
                variant="glass"
                size="sm"
                onClick={handleClickResetLocal}
              />
            )}
            <IconButton
              Icon={X}
              variant="glass"
              size="sm"
              onClick={handleClose}
              className="border-none shadow-none p-2"
            />
          </div>
        </DrawerHeader>
        <Form {...form}>
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="flex flex-col overflow-y-auto overflow-x-hidden *:not-last:mx-6 pt-6 gap-8 *:not-last:pb-8 bg-card-bg backdrop-blur-sm divide-y-[1px] divide-muted-foreground/40"
          >
            <DrawerSection
              title="💰 Price Range"
              badgeText={filters.priceLevel.length || undefined}
            >
              <CheckboxCardField
                control={control}
                options={filterOptions.priceLevels}
                name="priceLevel"
                gradient={gradient}
                labelFormatter={getPriceLevelText}
                className="grid sm:grid-cols-2 gap-3"
                size="default"
              />
            </DrawerSection>
            <DrawerSection
              title="⭐ Minimum Rating"
              badgeText={filters.rating > 0 ? `${filters.rating}+ stars` : undefined}
            >
              <SliderField
                control={control}
                name="rating"
                min={0}
                max={5}
                step={0.5}
                minDescription="Any rating"
                maxDescription="5+ stars"
              />
            </DrawerSection>
            <DrawerSection
              title="🏷️ Atmosphere & Style"
              badgeText={filters.tags.length || undefined}
            >
              <CheckboxCardField
                control={control}
                options={filterOptions.tags}
                name="tags"
                gradient={gradient}
              />
            </DrawerSection>
            {filterOptions.categories.length > 1 && (
              <DrawerSection
                title="📍 Place Type"
                badgeText={filters.categories.length || undefined}
              >
                <CheckboxCardField
                  control={control}
                  options={filterOptions.categories}
                  name="categories"
                  gradient={gradient}
                  size="default"
                  className="flex-col"
                />
              </DrawerSection>
            )}
            <DrawerSection title="🚶 Travel Time" badgeText={travelTimeBadgeText}>
              <RadioCardField
                control={control}
                options={filterOptions.travelTimes}
                name="travelTime"
                gradient={gradient}
                onValueChange={handleTravelTimeChange}
                labelFormatter={label => getTravelTimeCategory(Number(label)).text}
              />
            </DrawerSection>
            <DrawerSection
              title="✨ Features & Amenities"
              badgeText={filters.amenities.length || undefined}
            >
              <CheckboxCardField
                control={control}
                options={filterOptions.amenities}
                name="amenities"
                gradient={gradient}
              />
            </DrawerSection>
            <DrawerFooter className="flex-row bg-card-bg backdrop-blur-xl border-t border-muted-foreground/40 sticky bottom-0">
              <IconButton
                text="Reset All"
                Icon={RotateCcw}
                variant="card"
                className="rounded-md flex-1 gap-4 duration-300"
                onClick={handleClickResetAll}
                disabled={!activeFiltersCount}
              />
              <IconButton
                text="Apply Filters"
                Icon={Filter}
                badgeText={activeFiltersCount || undefined}
                variant="default"
                className={cn(
                  'rounded-md flex-1 gap-4 duration-300 bg-gradient-to-r text-white border-transparent hoverActive:opacity-80',
                  gradient
                )}
                type="submit"
                disabled={isEqual(filters, queryFilters)}
              />
            </DrawerFooter>
          </form>
        </Form>
      </DrawerContent>
    </Drawer>
  )
}

export default FilterDrawer
