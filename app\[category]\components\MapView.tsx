'use client'

import { useCallback, useEffect, useRef, useState } from 'react'
import { Category, Recommendation } from '@/types/types'
import { createRoot } from 'react-dom/client'
import { Map, Marker, Popup } from 'mapbox-gl'
import RecommendationMapMarker from './RecommendationMapMarker'
import RecommendationMapPopup from './RecommendationMapPopup'
import Mapbox from '@/components/Mapbox'

type Props = {
  recommendations: Recommendation[]
  category: Category
  gradient: string
}

const MapView = ({ recommendations, category, gradient }: Props) => {
  const map = useRef<Map>(null)
  const markers = useRef<Marker[]>([])
  const [mapLoaded, setMapLoaded] = useState(false)

  const addMarkersToMap = useCallback(() => {
    if (!map.current) return
    recommendations.forEach(
      (
        {
          id,
          name,
          images,
          rating,
          category: recommendationCategory,
          address,
          openingHours,
          coordinates,
          openingHoursInfo,
        },
        index
      ) => {
        const markerElement = document.createElement('div')
        const markerRoot = createRoot(markerElement)
        markerRoot.render(<RecommendationMapMarker gradient={gradient} index={index} />)

        const popupElement = document.createElement('div')
        const popupRoot = createRoot(popupElement)
        popupRoot.render(
          <RecommendationMapPopup
            category={category}
            gradient={gradient}
            id={id}
            name={name}
            image={images[0]}
            rating={rating}
            recommendationCategory={recommendationCategory}
            address={address}
            openingHours={openingHours}
            openingHoursInfo={openingHoursInfo}
          />
        )

        const popup = new Popup({
          offset: 25,
          closeOnClick: true,
          closeOnMove: false,
          className: 'map-popup w-full',
        }).setDOMContent(popupElement)

        const newMarker = new Marker(markerElement)
          .setLngLat(coordinates)
          .setPopup(popup)
          .addTo(map.current!)
        markers.current.push(newMarker)
      }
    )
  }, [category, gradient, recommendations])

  useEffect(() => {
    if (mapLoaded && map.current) {
      addMarkersToMap()
    }

    const removeMarkers = () => {
      markers.current?.forEach(marker => {
        marker.remove()
      })
      markers.current = []
    }

    return removeMarkers
  }, [recommendations, addMarkersToMap, mapLoaded])

  return (
    <Mapbox
      map={map}
      mapLoaded={mapLoaded}
      onMapLoaded={() => setMapLoaded(true)}
      recommendationsLength={recommendations.length}
    />
  )
}

export default MapView
