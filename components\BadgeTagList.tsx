import { Badge } from '@/components/UI/badge'
import { cn } from '@/lib/utils'

type Props = {
  tags: string[]
  maxVisible?: number
  size?: 'sm' | 'default'
  border?: boolean
}

const TagList = ({ tags, maxVisible, size = 'default', border = false }: Props) => {
  const visibleTags = !maxVisible ? tags : tags.slice(0, maxVisible)
  const remainingCount = !maxVisible ? 0 : tags.length - maxVisible

  return (
    <div className="flex flex-wrap gap-2 *:duration-300">
      {visibleTags.map((tag, index) => (
        <Badge
          key={tag}
          variant="card"
          className={cn('rounded-md text-secondary-text', border && 'border-glass-border')}
          style={{
            animationDelay: `${index * 100}ms`,
          }}
          size={size}
        >
          {tag}
        </Badge>
      ))}
      {remainingCount > 0 && (
        <Badge variant="card" className="rounded-md text-accent-text" size="sm">
          +{remainingCount}
        </Badge>
      )}
    </div>
  )
}

export default TagList
