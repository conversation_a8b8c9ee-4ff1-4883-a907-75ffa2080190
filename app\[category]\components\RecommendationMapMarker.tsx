import { cn } from '@/lib/utils'

type Props = {
  gradient: string
  index: number
}

const RecommendationMapMarker = ({ gradient, index }: Props) => (
  <div
    className={cn(
      'size-10 bg-gradient-to-br rounded-full border-3 border-white shadow-lg grid place-content-center text-white cursor-pointer hoverActive:scale-103 transition-transform duration-200',
      gradient
    )}
  >
    <span className="text-sm font-semibold">{index + 1}</span>
  </div>
)

export default RecommendationMapMarker
