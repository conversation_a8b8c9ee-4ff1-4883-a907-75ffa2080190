'use client'

import FormSection from './FormSection'
import { But<PERSON> } from '@/components/UI/button'
import { Input } from '@/components/UI/input'
import { Plus, Save, MapPin, Clock, Tag, Wifi, ImageIcon } from 'lucide-react'
import {
  defaultRecommendation,
  PRICE_LEVEL_MAP,
  RECOMMENDATION_CATEGORIES,
} from '@/data/recommendations'
import { COMMON_TAGS, COMMON_AMENITIES } from '@/data/mockData'
import { Form } from '@/components/UI/form'
import { z } from 'zod'
import IconButton from '@/components/IconButton'
import { useState } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { ImageFile, Recommendation } from '@/types/types'
import AddressPicker from './AddressPicker'
import ImageUploaderField from '@/components/Fields/ImageUploaderField'
import CheckboxCardField from '@/components/Fields/CheckboxCardField'
import InputField from '@/components/Fields/InputField'
import <PERSON>Field from '@/components/Fields/SelectField'
import OpeningH<PERSON>Field from '@/components/Fields/OpeningHoursField'

const getImageData = (images: string[]): ImageFile[] => {
  const initialImages = images.map((image, index) => ({
    id: `initial-${index}`,
    url: image,
    file: undefined,
    name: `Image ${index + 1}`,
  }))
  return initialImages
}

const dayHoursSchema = z.object({
  is24Hours: z.boolean().optional(),
  periods: z
    .array(
      z.object({
        openTime: z.string(),
        closeTime: z.string(),
      })
    )
    .optional(),
})

export const recommendationSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Short description is required'),
  rating: z.number('Rating is required').min(0).max(5),
  priceLevel: z
    .number('Price level is required')
    .min(0)
    .max(Number(Object.keys(PRICE_LEVEL_MAP).at(-1))),
  category: z.enum(RECOMMENDATION_CATEGORIES, {
    message: 'Category is required',
  }),
  address: z.string().min(1, 'Address is required'),
  tags: z.array(z.string()),
  detailedDescription: z.string().min(50, 'Detailed description is required (min 50 characters)'),
  amenities: z.array(z.string()),
  images: z
    .array(
      z.object({
        id: z.string(),
        url: z.string(),
        file: z.instanceof(File).optional().nullable(),
        name: z.string(),
      })
    )
    .min(1, 'At least one image is required'),
  coordinates: z.object({
    lat: z.number(),
    lng: z.number(),
  }),
  menuHighlights: z.array(z.string()),
  openingHours: z
    .object({
      monday: dayHoursSchema,
      tuesday: dayHoursSchema,
      wednesday: dayHoursSchema,
      thursday: dayHoursSchema,
      friday: dayHoursSchema,
      saturday: dayHoursSchema,
      sunday: dayHoursSchema,
    })
    .partial()
    .or(z.null().optional()),
  openingHoursInfo: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  website: z.string().optional().nullable(),
  email: z.email().optional().nullable(),
  social: z
    .object({
      instagram: z.string().optional().nullable(),
      facebook: z.string().optional().nullable(),
    })
    .optional()
    .nullable(),
  googleMaps: z.string().optional().nullable(),
})

type RecommendationFormValues = z.infer<typeof recommendationSchema>

type Props = {
  recommendation: Recommendation | null
  onClose: () => void
}

const getInitialValues = (recommendation: Recommendation | null): RecommendationFormValues => {
  if (!recommendation)
    return {
      ...defaultRecommendation,
    }
  return {
    ...recommendation,
    images: getImageData(recommendation.images),
  }
}

const RecommendationForm = ({ recommendation, onClose }: Props) => {
  const [newTag, setNewTag] = useState('')
  const [newAmenity, setNewAmenity] = useState('')
  const [tags, setTags] = useState(
    new Set<string>([...COMMON_TAGS, ...(recommendation?.tags || [])])
  )
  const [amenities, setAmenities] = useState(
    new Set<string>([...COMMON_AMENITIES, ...(recommendation?.amenities || [])])
  )
  const form = useForm<RecommendationFormValues>({
    resolver: zodResolver(recommendationSchema),
    defaultValues: getInitialValues(recommendation),
  })
  const { watch, handleSubmit, control, setValue, reset } = form

  const checkedTags = watch('tags')
  const checkedAmenities = watch('amenities')
  const openingHoursInfo = watch('openingHoursInfo')

  const onSubmit: SubmitHandler<RecommendationFormValues> = () => {
    onClose()
  }

  const handleClose = () => {
    reset()
    onClose()
  }

  const handleClickAddTag = () => {
    if (!newTag.trim()) return
    setTags(prev => new Set(prev.add(newTag.toLowerCase().trim())))
    setValue('tags', Array.from(new Set(checkedTags).add(newTag.toLowerCase().trim())))
    setNewTag('')
  }

  const handleClickAddAmenity = () => {
    if (!newAmenity.trim()) return
    setAmenities(prev => new Set(prev.add(newAmenity.trim())))
    setValue('amenities', Array.from(new Set(checkedAmenities).add(newAmenity.trim())))
    setNewAmenity('')
  }

  return (
    <Form {...form}>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-col *:pb-8 gap-8 divide-y-[1px] divide-muted-foreground/40"
      >
        <FormSection title="Basic Information" icon={MapPin}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputField
              control={control}
              name="name"
              label="Name"
              placeholder="Enter place name"
              schema={recommendationSchema}
            />
            <SelectField
              control={control}
              name="category"
              label="Category"
              options={[...RECOMMENDATION_CATEGORIES]}
              placeholder="Select category"
              schema={recommendationSchema}
            />
          </div>

          <InputField
            as="textarea"
            control={control}
            name="description"
            label="Description"
            placeholder="Short description"
            schema={recommendationSchema}
          />
          <InputField
            as="textarea"
            control={control}
            name="detailedDescription"
            label="Detailed Description"
            placeholder="Detailed description"
            schema={recommendationSchema}
          />
        </FormSection>
        <FormSection title="Image Gallery" icon={ImageIcon}>
          <ImageUploaderField />
        </FormSection>
        <FormSection title="Location" icon={MapPin}>
          <AddressPicker />
        </FormSection>
        <FormSection title="Details" icon={Clock}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SelectField
              control={control}
              name="priceLevel"
              label="Price Level"
              options={PRICE_LEVEL_MAP}
              placeholder="Select price level"
              schema={recommendationSchema}
            />
            <InputField
              control={control}
              name="rating"
              label="Rating"
              type="number"
              min="0"
              max="5"
              step="0.1"
              schema={recommendationSchema}
            />
          </div>
          <OpeningHoursField
            control={control}
            description={openingHoursInfo}
            schema={recommendationSchema}
          />
          <InputField
            control={control}
            name="openingHoursInfo"
            label="Hours Info"
            placeholder="Additional hours info"
            schema={recommendationSchema}
          />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <InputField
              control={control}
              name="website"
              label="Website"
              placeholder="https://example.com"
              schema={recommendationSchema}
            />
            <InputField
              control={control}
              name="phone"
              label="Phone"
              placeholder="+36 XX XXX XXXX"
              type="tel"
              schema={recommendationSchema}
            />
            <InputField
              control={control}
              name="email"
              label="Email"
              type="email"
              placeholder="<EMAIL>"
              schema={recommendationSchema}
            />
            <InputField
              control={control}
              name="social.instagram"
              label="Instagram"
              placeholder="@username"
              schema={recommendationSchema}
            />
            <InputField
              control={control}
              name="social.facebook"
              label="Facebook"
              placeholder="https://facebook.com/username"
              schema={recommendationSchema}
            />
          </div>
        </FormSection>
        <FormSection title="Tags" icon={Tag}>
          <CheckboxCardField
            options={Array.from(tags)}
            control={control}
            name="tags"
            gradient="from-indigo-500 to-purple-600"
          />
          <div className="flex gap-2 items-center">
            <Input
              value={newTag}
              onChange={e => setNewTag(e.target.value)}
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  handleClickAddTag()
                }
              }}
              placeholder="Add custom tag"
            />
            <Button
              type="button"
              onClick={handleClickAddTag}
              size="sm"
              variant="active"
              className="rounded-lg"
            >
              <Plus className="size-4" />
            </Button>
          </div>
          <CheckboxCardField
            options={checkedTags}
            control={control}
            name="tags"
            labelFormatter={label => label + ' X'}
            gradient="from-indigo-500 to-purple-600"
          />
        </FormSection>
        <FormSection title="Amenities" icon={Wifi}>
          <CheckboxCardField
            options={Array.from(amenities)}
            control={control}
            name="amenities"
            gradient="from-emerald-500 to-teal-600"
          />
          <div className="flex gap-2 items-center">
            <Input
              value={newAmenity}
              onChange={e => setNewAmenity(e.target.value)}
              onKeyDown={e => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  handleClickAddAmenity()
                }
              }}
              placeholder="Add custom amenity"
            />
            <Button
              type="button"
              onClick={handleClickAddAmenity}
              size="sm"
              variant="active"
              className="rounded-lg"
            >
              <Plus className="size-4" />
            </Button>
          </div>
          <CheckboxCardField
            options={checkedAmenities}
            control={control}
            name="amenities"
            labelFormatter={label => label + ' X'}
            gradient="from-emerald-500 to-teal-600"
          />
        </FormSection>
        <div className="flex flex-col sm:flex-row gap-4 !py-0">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            className="flex-1 !text-gray-700 !bg-white !border-gray-300 hoverActive:!bg-gray-50 hoverActive:!text-gray-900 rounded-md"
          >
            Cancel
          </Button>
          <IconButton
            Icon={Save}
            text={recommendation?.id ? 'Update Recommendation' : 'Create Recommendation'}
            type="submit"
            className="flex-1 bg-gradient-to-r from-indigo-500 to-purple-600 !text-white border-transparent hoverActive:opacity-90 rounded-md"
          />
        </div>
      </form>
    </Form>
  )
}

export default RecommendationForm
