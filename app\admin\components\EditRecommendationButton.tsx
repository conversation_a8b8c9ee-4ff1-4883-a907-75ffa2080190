'use client'

import { MouseEvent } from 'react'
import { Edit } from 'lucide-react'
import IconButton from '@/components/IconButton'
import { Recommendation } from '@/types/types'
import { useQueryState } from 'nuqs'

type Props = {
  recommendation: Recommendation
}

const EditRecommendationButton = ({ recommendation }: Props) => {
  const [, setId] = useQueryState('id')

  const handleClickEdit = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    setId(recommendation.id)
  }

  return (
    <IconButton
      variant="card"
      Icon={Edit}
      onClick={handleClickEdit}
      className="p-2 h-auto hoverActive:text-blue-600"
    />
  )
}

export default EditRecommendationButton
