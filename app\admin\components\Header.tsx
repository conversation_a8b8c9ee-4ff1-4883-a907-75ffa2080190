'use client'

import IconButton from '@/components/IconButton'
import { Recommendation } from '@/types/types'
import { Settings, Plus } from 'lucide-react'
import ImportButton from '@/components/ImportButton'
import ExportButton from '@/components/ExportButton'
import { useQueryState } from 'nuqs'
import { recommendationSchema } from './RecommendationForm'

type Props = {
  recommendations: Recommendation[]
}

const Header = ({ recommendations }: Props) => {
  const [, setId] = useQueryState('id')

  const handleImport = (value: Recommendation[] | Recommendation) => {
    if (!Array.isArray(value)) value = [value]
    value.forEach(recommendation => {
      const result = recommendationSchema.safeParse({
        ...recommendation,
        images: recommendation.images.map((image, index) => ({
          id: `initial-${index}`,
          url: image,
          file: undefined,
          name: `Image ${index + 1}`,
        })),
      })
      if (!result.success) {
        console.error('Invalid recommendation: ', result.error)
        return
      }
      // Upload images to bucket and update recommendation with new image URLs
      // Upload recommendation to Database after that without currect ID's that they might have
    })
  }

  const handleAddNew = () => {
    setId('new')
  }

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 shadow-sm z-50">
      <div className="flex items-center justify-between gap-4 flex-col md:flex-row max-w-screen-xl mx-auto p-4">
        <div className="flex items-center gap-6">
          <div className="flex items-center gap-4">
            <div className="size-10 rounded-lg bg-slate-600 flex items-center justify-center">
              <Settings size={20} color="white" />
            </div>
            <div>
              <h1 className="text-xl font-semibold">Admin Dashboard</h1>
              <p className="text-sm text-gray-600">QR Guide Management Console</p>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-center flex-wrap gap-3">
          <ImportButton onImport={handleImport} />
          <ExportButton data={recommendations} fileName="qr-guide-recommendations" />
          <IconButton
            variant="active"
            size="sm"
            Icon={Plus}
            text="Add New"
            onClick={handleAddNew}
            className="rounded-lg"
          />
        </div>
      </div>
    </header>
  )
}

export default Header
