import IconButton from '@/components/IconButton'
import { Badge } from '@/components/UI/badge'
import { cn } from '@/lib/utils'
import { AlertTriangle, Plus, Trash2 } from 'lucide-react'

type Props = {
  periodIndex: number
  periodsLength: number
  onAdd: () => void
  onRemove: () => void
  error?: string
}

const OpeningHourPeriodActionButtons = ({
  periodIndex,
  periodsLength,
  onAdd,
  onRemove,
  error,
}: Props) => (
  <div className="flex items-center justify-between">
    <Badge variant="outline" className={cn('py-0', error && 'border-red-300 text-red-700')}>
      {error && <AlertTriangle size={8} />}#{periodIndex + 1}
    </Badge>
    <div className="flex items-center gap-1">
      {periodsLength >= 1 && (
        <>
          <IconButton
            variant="ghost"
            size="sm"
            Icon={Plus}
            onClick={onAdd}
            className="px-2 text-blue-600 hoverActive:!text-blue-700"
          />
          {periodsLength > 1 && (
            <IconButton
              type="button"
              variant="ghost"
              size="sm"
              Icon={Trash2}
              onClick={onRemove}
              className="px-2 text-red-500 hoverActive:!text-red-700"
            />
          )}
        </>
      )}
    </div>
  </div>
)

export default OpeningHourPeriodActionButtons
