import Link from 'next/link'
import { ArrowLeft } from 'lucide-react'

const Footer = () => (
  <footer className="mt-12 border-t border-gray-200 bg-gray-50/50">
    <div className="max-w-screen-xl mx-auto px-6 py-4 flex items-center justify-between">
      <p className="text-sm text-gray-600">QR Guide Admin Panel © 2025</p>
      <Link
        href="/"
        className="flex items-center gap-2 text-sm text-gray-600 hoverActive:text-gray-800 transition-colors duration-300"
      >
        <ArrowLeft className="size-4" />
        Back to App
      </Link>
    </div>
  </footer>
)

export default Footer
