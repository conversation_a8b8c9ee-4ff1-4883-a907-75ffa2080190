'use client'

import { Download } from 'lucide-react'
import React, { useEffect, useRef } from 'react'
import IconButton from './IconButton'
import Link from 'next/link'

type Props = {
  data: unknown[]
  fileName: string
}

const ExportButton = ({ data, fileName }: Props) => {
  const exportRef = useRef<HTMLAnchorElement>(null)

  useEffect(() => {
    if (exportRef.current) {
      const dataStr = JSON.stringify(data, null, 2)

      exportRef.current.href = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`
      exportRef.current.download = `${fileName}.json`
    }
  }, [data, fileName])

  return (
    <>
      <Link href="" ref={exportRef} className="hidden">
        Export
      </Link>
      <IconButton
        variant="outline"
        size="sm"
        Icon={Download}
        text="Export"
        onClick={() => exportRef.current?.click()}
        className="rounded-lg"
      />
    </>
  )
}

export default ExportButton
