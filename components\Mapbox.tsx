'use client'

import { isMapboxConfigured, MAPBOX_CONFIG } from '@/lib/mapConfig'
import { cn } from '@/lib/utils'
import React, { RefObject, useEffect, useRef, useState } from 'react'
import MapOverlay from './MapOverlay'
import MapStatus from './MapStatus'
import { Map } from 'mapbox-gl'
import { DEFAULT_PLACE } from '@/data/mockData'

type Props = {
  map: RefObject<Map | null>
  mapLoaded: boolean
  onMapLoaded: () => void
  isEditing?: boolean
  recommendationsLength?: number
}

const Mapbox = ({
  map,
  mapLoaded,
  onMapLoaded,
  isEditing = false,
  recommendationsLength,
}: Props) => {
  const initialized = useRef(false)
  const mapContainer = useRef<HTMLDivElement>(null)
  const [isError, setIsError] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)

  useEffect(() => {
    if (initialized.current) return
    initialized.current = true
    if (map.current || !mapContainer.current || !isMapboxConfigured()) {
      setIsError(true)
      return
    }

    const initializeMap = async () => {
      try {
        const mapboxgl = await import('mapbox-gl')
        mapboxgl.default.accessToken = MAPBOX_CONFIG.accessToken

        map.current = new mapboxgl.default.Map({
          container: mapContainer.current!,
          style: MAPBOX_CONFIG.style,
          center: DEFAULT_PLACE.coordinates,
          zoom: MAPBOX_CONFIG.zoom,
          attributionControl: false,
        })

        map.current.on('load', () => onMapLoaded())
        map.current.on('error', () => setIsError(true))
        map.current.addControl(new mapboxgl.default.NavigationControl(), 'top-right')
      } catch {
        setIsError(true)
      }
    }

    initializeMap()

    return () => {
      if (map.current) {
        map.current.remove()
        map.current = null
      }
    }
  }, [onMapLoaded, map])

  useEffect(() => {
    if (map.current && mapLoaded) map.current.resize()
  }, [isFullscreen, map, mapLoaded])

  return (
    <section
      className={cn(
        isFullscreen ? 'fixed inset-0 z-50 rounded-none' : 'relative h-64 rounded-lg',
        'p-0 bg-slate-100 overflow-hidden shadow-lg transition-all duration-300'
      )}
    >
      <div
        ref={mapContainer}
        className={cn('relative size-full overflow-hidden', (!mapLoaded || isError) && 'hidden')}
      >
        <MapOverlay
          isFullscreen={isFullscreen}
          toggleFullscreen={() => setIsFullscreen(prev => !prev)}
          isEditing={isEditing}
          recommendationsLength={recommendationsLength}
        />
      </div>
      <MapStatus isError={isError} mapLoaded={mapLoaded} />
    </section>
  )
}

export default Mapbox
