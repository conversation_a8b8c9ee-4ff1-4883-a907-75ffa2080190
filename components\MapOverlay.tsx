import IconBadge from '@/components/IconBadge'
import { Minimize2, Maximize2, MapPin } from 'lucide-react'
import IconButton from './IconButton'
import pluralize from 'pluralize'

type Props = {
  isFullscreen: boolean
  toggleFullscreen: () => void
  isEditing?: boolean
  recommendationsLength?: number
}

const MapOverlay = ({
  isEditing,
  recommendationsLength,
  isFullscreen,
  toggleFullscreen,
}: Props) => (
  <>
    <IconButton
      Icon={isFullscreen ? Minimize2 : Maximize2}
      onClick={toggleFullscreen}
      variant="card"
      className="absolute top-4 left-4 z-20 p-2 !bg-white border rounded-lg text-slate-700"
    />
    {(isEditing || recommendationsLength) && (
      <IconBadge
        Icon={MapPin}
        text={
          isEditing ? 'Click map or drag marker' : pluralize('place', recommendationsLength, true)
        }
        variant="card"
        className="absolute bottom-4 right-4 z-20 py-1 !bg-white text-slate-700"
        size="sm"
      />
    )}
  </>
)

export default MapOverlay
