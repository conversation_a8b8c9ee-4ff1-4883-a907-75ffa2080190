import Header from './components/Header'
import List from './components/RecommendationsList'
import Footer from './components/Footer'
import ThemeHolder from './components/ThemeHolder'
import RecommendationFormDialog from './components/RecommendationFormDialog'
import flatMap from 'lodash/flatMap'
import { getRecommendationsWithTravelInfo } from '@/lib/recommendationHelper'

const allRecommendations = flatMap(getRecommendationsWithTravelInfo())

const page = async () => {
  await new Promise(resolve => setTimeout(resolve, 200))

  return (
    <div className="relative min-h-screen bg-gray-50 text-gray-900 flex flex-col overflow-x-hidden">
      <RecommendationFormDialog />
      <ThemeHolder />
      <Header recommendations={allRecommendations} />
      <List recommendations={allRecommendations} />
      <Footer />
    </div>
  )
}

export default page
