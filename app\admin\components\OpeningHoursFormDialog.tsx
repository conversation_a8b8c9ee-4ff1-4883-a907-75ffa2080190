'use client'

import { useState, ReactNode } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/UI/dialog'
import { ScrollArea } from '@/components/UI/scroll-area'
import { DayHours, OpeningHoursData } from '@/types/types'
import OpeningHoursForm from './OpeningHoursForm'
import { Clock } from 'lucide-react'

type Props = {
  value: OpeningHoursData
  onChange: (openHours: OpeningHoursData | null) => void
  children: ReactNode
}

const OpeningHoursFormDialog = ({ value, onChange, children }: Props) => {
  const [modalIsOpen, setModalIsOpen] = useState(false)

  const handleSave = (openHours: OpeningHoursData) => {
    const nullifiedClosedDays: Record<string, DayHours> = {}
    for (const [day, dayHours] of Object.entries(openHours)) {
      if (dayHours.periods?.length === 0 && !dayHours.is24Hours) continue
      if (dayHours.is24Hours) {
        nullifiedClosedDays[day] = { is24Hours: true }
        continue
      }
      nullifiedClosedDays[day] = { periods: dayHours.periods }
    }

    onChange(nullifiedClosedDays)
    setModalIsOpen(false)
  }

  const handleCancel = () => setModalIsOpen(false)

  return (
    <Dialog open={modalIsOpen} onOpenChange={setModalIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="w-[90%] max-w-4xl border">
        <DialogHeader className="border-b border-gray-200 pb-2">
          <DialogTitle className="flex items-center gap-2">
            <div className="size-8 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center">
              <Clock size={16} color="white" />
            </div>
            Opening Hours
          </DialogTitle>
          <DialogDescription className="text-gray-600 text-sm">
            Set business hours with multiple periods per day
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="h-[75dvh] pr-4 -mr-4">
          <OpeningHoursForm onSave={handleSave} onCancel={handleCancel} openHours={value} />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}

export default OpeningHoursFormDialog
