import { notFound } from 'next/navigation'
import RecommendationListHeader from './components/RecommendationListHeader'
import RecommendationList from './components/RecommendationList'
import CategoryHeader from './components/CategoryHeader'
import { categories as categoriesArray, Category, FilterState } from '@/types/types'
import RecommendationListTypeProvider from '@/components/RecommendationListTypeProvider'
import FilterDrawer from './components/FilterDrawer'
import { searchAndFilterRecommendations, searchRecommendations } from '@/lib/searchUtils'
import { defaultFilterState } from '@/lib/filterUtils'
import isEqual from 'lodash/isEqual'
import { searchParamsCache } from '@/lib/searchParams'
import type { SearchParams } from 'nuqs/server'
import { getRecommendationsWithTravelInfo } from '@/lib/recommendationHelper'

type Props = {
  params: Promise<{ category: Category }>
  searchParams: Promise<SearchParams>
}

const allRecommendations = getRecommendationsWithTravelInfo()

const CategoryPage = async ({ params, searchParams }: Props) => {
  const { category } = await params
  const { q, priceLevel, rating, tags, amenities, travelTime, categories } =
    await searchParamsCache.parse(searchParams)

  if (!categoriesArray.includes(category)) {
    notFound()
  }

  const parsedFilters: FilterState = {
    priceLevel,
    rating,
    tags,
    amenities,
    travelTime,
    categories,
  }

  const recommendations = allRecommendations[category]

  const filteredRecommendations = !isEqual(parsedFilters, defaultFilterState)
    ? searchAndFilterRecommendations(recommendations, q || '', parsedFilters)
    : searchRecommendations(recommendations, q || '')

  return (
    <RecommendationListTypeProvider>
      <CategoryHeader
        category={category}
        numberOfRecommendations={filteredRecommendations.length}
      />
      <main>
        <RecommendationListHeader
          category={category}
          numberOfRecommendations={filteredRecommendations.length}
          searchParamsFilters={parsedFilters}
        />
        <RecommendationList category={category} recommendations={filteredRecommendations} />
      </main>
      <FilterDrawer category={category} recommendations={recommendations} />
    </RecommendationListTypeProvider>
  )
}

export default CategoryPage
