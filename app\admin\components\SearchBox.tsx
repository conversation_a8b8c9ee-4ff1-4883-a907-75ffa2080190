'use client'

import { DEFAULT_PLACE } from '@/data/mockData'
import { MAPBOX_CONFIG } from '@/lib/mapConfig'
import { SearchBox as MapboxSearchBox } from '@mapbox/search-js-react'
import { useState } from 'react'

type Props = {
  updateLocation: (coordinates: { lng: number; lat: number; placeAddress?: string }) => void
}

const SearchBox = ({ updateLocation }: Props) => {
  const [searchTerm, setSearchTerm] = useState('')

  return (
    <MapboxSearchBox
      accessToken={MAPBOX_CONFIG.accessToken || ''}
      options={{
        language: 'en',
        country: 'hu',
        proximity: DEFAULT_PLACE.coordinates,
        limit: 5,
      }}
      value={searchTerm}
      onChange={val => setSearchTerm(val)}
      onRetrieve={result => {
        const { features } = result
        if (!features.length) return
        const {
          geometry: {
            coordinates: [lng, lat],
          },
          properties: { full_address: placeAddress },
        } = features[0]
        updateLocation({ lng, lat, placeAddress })
      }}
      placeholder={`Search for an address in ${DEFAULT_PLACE.city}...`}
    />
  )
}

export default SearchBox
