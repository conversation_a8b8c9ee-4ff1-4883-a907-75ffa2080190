'use client'

import { useEffect, useRef } from 'react'

const ThemeHolder = () => {
  const htmlRef = useRef<HTMLElement>(null)

  useEffect(() => {
    htmlRef.current = document.documentElement
    const html = htmlRef.current
    if (!html) return

    const originalClasses = html.className
    html.classList.remove(originalClasses)

    return () => {
      html.className = originalClasses
    }
  }, [])

  return null
}

export default ThemeHolder
