import { useState } from 'react'
import { Control, FieldValues, Path } from 'react-hook-form'
import { FormField, FormItem } from '@/components/UI/form'
import CheckboxCard from '@/components/CheckboxCard'
import { cn } from '@/lib/utils'
import { Button } from '@/components/UI/button'

type Props<T extends FieldValues, U extends string | number> = {
  control: Control<T>
  options: U[]
  name: Path<T>
  gradient: string
  size?: 'sm' | 'default'
  className?: string
  labelFormatter?: (label: U) => string
}

const CheckboxCardField = <T extends FieldValues, U extends string | number>({
  control,
  options,
  name,
  gradient,
  size = 'sm',
  className,
  labelFormatter,
}: Props<T, U>) => {
  const [showMore, setShowMore] = useState(false)

  if (!options.length) return null

  return (
    <FormField
      control={control}
      name={name}
      render={({ field: { value, onChange } }) => (
        <FormItem className={cn('flex flex-row flex-wrap gap-2', className)}>
          {options.slice(0, showMore ? options.length : 15).map(tag => (
            <CheckboxCard
              key={String(tag)}
              name={name}
              label={labelFormatter ? labelFormatter(tag) : String(tag)}
              checked={value.includes(tag)}
              onCheckedChange={checked =>
                onChange(checked ? [...value, tag] : value.filter((t: U) => t !== tag))
              }
              gradient={gradient}
              size={size}
            />
          ))}
          {options.length > 15 && (
            <Button
              variant="link"
              size="sm"
              type="button"
              onClick={() => setShowMore(prev => !prev)}
              className="text-xs px-0 h-auto"
            >
              {showMore ? 'Show less' : 'Show more'}
            </Button>
          )}
        </FormItem>
      )}
    />
  )
}

export default CheckboxCardField
