import { Category } from '@/types/types'
import { notFound } from 'next/navigation'
import { getCategoryDetailsById } from '@/lib/categoryHelper'
import RecommendationHeader from './components/RecommendationHeader'
import GlassSeperator from '@/components/GlassSeperator'
import Footer from './components/Footer'
import ContactsHoursSection from './components/ContactsHoursSection'
import Highlights from './components/HighlightsSection'
import MainInformation from './components/MainInformationSection'
import Reviews from './components/ReviewsSection'
import { getRecommendationById } from '@/data/mockData'

type Props = {
  params: Promise<{ category: Category; recommendationId: string }>
}

const DetailsPage = async ({ params }: Props) => {
  const { category, recommendationId } = await params

  const recommendation = getRecommendationById(recommendationId)

  if (!recommendation) notFound()

  const { gradient, iconColor } = getCategoryDetailsById(category)

  return (
    <>
      <RecommendationHeader recommendation={recommendation} />
      <main className="max-w-md mx-auto space-y-8">
        <MainInformation categoryId={category} recommendation={recommendation} />
        <GlassSeperator />
        <Highlights
          gradient={gradient}
          iconColor={iconColor}
          menuHighlights={recommendation.menuHighlights}
        />
        <GlassSeperator />
        <ContactsHoursSection
          gradient={gradient}
          iconColor={iconColor}
          recommendation={recommendation}
        />
        <GlassSeperator />
        <Reviews gradient={gradient} iconColor={iconColor} reviews={recommendation.reviews} />
        <GlassSeperator className="mb-8" />
      </main>
      <Footer iconColor={iconColor} recommendation={recommendation} />
    </>
  )
}

export default DetailsPage
