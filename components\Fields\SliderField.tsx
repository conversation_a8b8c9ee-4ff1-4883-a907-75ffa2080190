import { Control, FieldValues, Path } from 'react-hook-form'
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/UI/form'
import { Slider } from '@/components/UI/slider'

type Props<T extends FieldValues> = {
  control: Control<T>
  name: Path<T>
  min: number
  max: number
  step: number
  label?: string
  minDescription?: string
  maxDescription?: string
}

const SliderField = <T extends FieldValues>({
  control,
  name,
  min,
  max,
  step,
  label,
  minDescription = '',
  maxDescription = '',
}: Props<T>) => (
  <FormField
    control={control}
    name={name}
    render={({ field: { value, onChange } }) => (
      <FormItem>
        {label && <FormLabel className="text-sm">{label}</FormLabel>}
        <FormControl>
          <Slider
            value={[value]}
            onValueChange={(val: number[]) => onChange(val[0])}
            min={min}
            max={max}
            step={step}
          />
        </FormControl>
        <FormDescription className="flex justify-between text-xs text-secondary-text">
          <span>{minDescription}</span>
          <span>{maxDescription}</span>
        </FormDescription>
        <FormMessage />
      </FormItem>
    )}
  />
)

export default SliderField
