'use client'

import IconButton from '@/components/IconButton'
import { ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'

type Props = {
  buttonClassName?: string
  iconClassName?: string
}

const BackButton = ({ buttonClassName, iconClassName }: Props) => {
  const router = useRouter()

  const handleClick = () => router.back()

  return (
    <IconButton
      className={buttonClassName}
      onClick={handleClick}
      Icon={ArrowLeft}
      iconClassName={iconClassName}
      text="Back"
    />
  )
}

export default BackButton
