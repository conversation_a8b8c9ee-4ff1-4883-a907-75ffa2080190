import GlassCard from '@/components/GlassCard'
import StarRating from '@/components/StarRating'
import {
  CardHeader,
  CardTitle,
  CardAction,
  CardContent,
  CardDescription,
  CardFooter,
} from '@/components/UI/card'
import { cn } from '@/lib/utils'
import { CategoryDetails, Recommendation } from '@/types/types'
import { Avatar, AvatarFallback } from '@/components/UI/avatar'
import { Users } from 'lucide-react'
import DetailSection from './DetailSection'
import { getReviewerName } from '@/lib/recommendationHelper'

type Props = {
  gradient: CategoryDetails['gradient']
  iconColor: CategoryDetails['iconColor']
  reviews: Recommendation['reviews']
}

const ReviewsSection = ({ gradient, iconColor, reviews }: Props) => (
  <DetailSection
    Icon={Users}
    title={`Reviews (${reviews.length})`}
    iconColor={iconColor}
    animationDelay={450}
  >
    <div className="space-y-4">
      {reviews.map(({ id, author, rating, comment, date }) => (
        <GlassCard
          variant="frostedGlass"
          key={id}
          containerClassName="p-4"
          className="flex-row items-start gap-3 **:transition-colors **:duration-300"
        >
          <Avatar className="size-10 text-white">
            <AvatarFallback className={cn('bg-gradient-to-br', gradient)}>A</AvatarFallback>
          </Avatar>
          <div className="flex flex-col gap-2 *:p-0">
            <CardHeader className="items-center">
              <CardTitle className="row-span-2 text-base text-primary-text">
                {getReviewerName(author)}
              </CardTitle>
              <CardAction>
                <StarRating rating={rating} />
              </CardAction>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-secondary-text leading-relaxed">
                {comment}
              </CardDescription>
            </CardContent>
            {date && <CardFooter className="text-accent-text text-xs">{date}</CardFooter>}
          </div>
        </GlassCard>
      ))}
    </div>
  </DetailSection>
)

export default ReviewsSection
