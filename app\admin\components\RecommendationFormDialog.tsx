'use client'

import { useEffect, useState } from 'react'
import { Recommendation } from '@/types/types'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/UI/dialog'
import { ScrollArea } from '@/components/UI/scroll-area'
import { Plus, Save } from 'lucide-react'
import RecommendationForm from './RecommendationForm'
import CircleIconWithText from '@/components/CircleIconWithText'
import { useQueryState } from 'nuqs'
import { getRecommendationById } from '@/data/mockData'

const RecommendationFormDialog = () => {
  const [id, setId] = useQueryState('id')
  const [recommendation, setRecommendation] = useState<Recommendation | null>(null)

  useEffect(() => {
    if (!id) return
    if (id === 'new') {
      setRecommendation(null)
      return
    }
    try {
      const initialRecommendation = getRecommendationById(id)
      setRecommendation(initialRecommendation)
    } catch (error) {
      console.error('Error fetching recommendation: ', error)
    }
  }, [id])

  const handleClose = () => {
    setRecommendation(null)
    setId(null)
  }

  return (
    <Dialog open={!!id} onOpenChange={isOpen => !isOpen && handleClose()}>
      <DialogContent className="sm:w-[95%] sm:max-w-screen-md border border-gray-200 shadow-xl">
        <DialogHeader className="border-b border-gray-200 pb-4">
          <DialogTitle>
            <CircleIconWithText
              gradient="from-indigo-500 to-purple-600"
              icon={recommendation?.id ? Save : Plus}
              text={recommendation?.id ? 'Edit Recommendation' : 'Add New Recommendation'}
            />
          </DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[80dvh] pr-4 -mr-4">
          <RecommendationForm recommendation={recommendation} onClose={handleClose} />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
}

export default RecommendationFormDialog
