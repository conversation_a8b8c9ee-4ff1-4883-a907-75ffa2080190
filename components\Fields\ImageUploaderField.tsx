'use client'

import IconBadge from '@/components/IconBadge'
import IconButton from '@/components/IconButton'
import ImageWithFallback from '@/components/ImageWithFallback'
import { Card, CardContent, CardHeader } from '@/components/UI/card'
import { FormField, FormItem, FormLabel, FormMessage } from '@/components/UI/form'
import { cn } from '@/lib/utils'
import { Upload, Eye, Plus, StarIcon, Trash2, Move } from 'lucide-react'
import pluralize from 'pluralize'
import { ChangeEvent, DragEvent, useRef, useState } from 'react'
import { useFormContext, useWatch } from 'react-hook-form'
import { toast } from 'sonner'
import { ImageFile } from '@/types/types'
import IMAGE_MAX_SIZE from '@/constants/uploadImageSize'

const ImageUploaderField = () => {
  const { control, setValue, getValues } = useFormContext()
  const imageGallery: ImageFile[] = useWatch({ control, name: 'images' })
  const fileInputRef = useRef<HTMLInputElement>(null)
  const draggedIndexRef = useRef<number | null>(null)
  const [dragOver, setDragOver] = useState(false)

  const imagesUpload = (files: File[]) => {
    files.forEach(file => {
      if (!file.type.startsWith('image/')) {
        toast.error(`${file.name} is not a valid image file`)
        return
      }
      if (file.size >= IMAGE_MAX_SIZE) {
        toast.error(`${file.name} is larger than ${IMAGE_MAX_SIZE / 1024 / 1024}MB`)
        return
      }

      const reader = new FileReader()
      reader.onload = e => {
        const newImage: ImageFile = {
          id: `upload-${Date.now()}-${Math.random()}`,
          url: e.target?.result as string,
          file,
          name: file.name,
        }
        const newGallery = [...getValues('images'), newImage]
        setValue('images', newGallery, { shouldValidate: true })
      }
      reader.readAsDataURL(file)
    })
  }

  const handleFileUpload = (event: ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length > 0) imagesUpload(files)
    if (event.target) event.target.value = ''
  }

  const handleImageDragStart = (index: number) => {
    draggedIndexRef.current = index
  }

  const handleImageDrop = (e: DragEvent, dropIndex: number) => {
    e.preventDefault()
    if (draggedIndexRef.current === null) return

    const draggedItem = imageGallery[draggedIndexRef.current]
    const newGallery = [...imageGallery]
    newGallery.splice(draggedIndexRef.current, 1)
    newGallery.splice(dropIndex, 0, draggedItem)

    setValue('images', newGallery, { shouldValidate: true })
    draggedIndexRef.current = null
  }

  const removeImage = (index: number) => {
    const newGallery = imageGallery.filter((_, i) => i !== index)
    setValue('images', newGallery, { shouldValidate: true })
    toast.success('Image removed')
  }

  const handleFileDragOver = (e: DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.dataTransfer.items.length > 0) setDragOver(true)
  }

  const handleFileDragDrop = (e: DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(false)
    imagesUpload([...e.dataTransfer.files])
  }

  return (
    <FormField
      control={control}
      name="images"
      render={({ fieldState: { error } }) => (
        <FormItem>
          <FormLabel>Upload Images *</FormLabel>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
          />
          <button
            type="button"
            className={cn(
              'flex flex-col items-center gap-4 border-2 border-dashed border-gray-400 rounded-lg p-8 text-center hoverActive:border-blue-400 transition-colors duration-300',
              dragOver && 'border-blue-400',
              error && 'border-red-500'
            )}
            onClick={() => fileInputRef.current?.click()}
            onMouseLeave={() => setDragOver(false)}
            onDragLeave={() => setDragOver(false)}
            onDragOver={handleFileDragOver}
            onDrop={handleFileDragDrop}
          >
            <div className="flex items-center justify-center size-12 rounded-full bg-blue-100 text-blue-600">
              <Upload size={24} />
            </div>
            <div>
              <p className={cn('text-lg font-medium', error && 'text-red-500')}>Upload Images *</p>
              <p className="text-sm text-gray-600">Click to browse or drag and drop images here</p>
              <p className="text-gray-400">Supports JPG, PNG, GIF up to 10MB each</p>
            </div>
          </button>
          <p className="flex items-center gap-2 text-sm text-gray-600">
            <Eye size={16} />
            First image will be used as the main display image
          </p>

          {imageGallery?.length > 0 && (
            <div className="flex flex-col gap-4 mt-4">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium">
                  {pluralize('image', imageGallery.length, true)} uploaded
                </h4>
                <IconButton
                  variant="outline"
                  size="sm"
                  Icon={Plus}
                  text="Add More"
                  onClick={() => fileInputRef.current?.click()}
                  className="rounded-lg"
                />
              </div>

              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {imageGallery.map(({ id, name, url }: ImageFile, idx: number) => (
                  <Card
                    key={id}
                    className="group py-0 gap-0 rounded-md overflow-hidden hoverActive:shadow-md duration-300 transition-shadow cursor-grab active:cursor-grabbing"
                    draggable
                    onDragStart={() => handleImageDragStart(idx)}
                    onDragOver={e => e.preventDefault()}
                    onDrop={e => handleImageDrop(e, idx)}
                  >
                    <CardHeader className="p-0 aspect-square relative">
                      <IconBadge
                        Icon={StarIcon}
                        text="Primary"
                        className={cn(
                          'absolute top-2 left-2 z-10 text-xs bg-blue-600 py-1 px-2 text-white opacity-0 transition-opacity',
                          idx === 0 && 'opacity-100'
                        )}
                      />
                      <ImageWithFallback
                        src={url}
                        alt={name}
                        className="absolute size-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black/50 opacity-0 group-hoverActive:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-2">
                        <IconButton
                          variant="destructive"
                          Icon={Trash2}
                          onClick={() => removeImage(idx)}
                          className="backdrop-blur-md p-2.5 rounded-full"
                          title="Remove image"
                        />
                      </div>
                    </CardHeader>
                    <CardContent className="flex flex-col gap-1 p-2 text-xs">
                      <p className="truncate">{name}</p>
                      <p className="flex items-center gap-2 text-gray-400">
                        <Move size={12} />
                        Drag to reorder
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          <FormMessage />
        </FormItem>
      )}
    />
  )
}

export default ImageUploaderField
