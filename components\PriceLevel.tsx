import { cn } from '@/lib/utils'
import { DollarSign } from 'lucide-react'
import { getPriceLevels } from '@/lib/recommendationHelper'
import { Badge } from '@/components/UI/badge'
import { ComponentProps } from 'react'
import { PriceLevel as PriceLevelType } from '@/types/types'

type Props = ComponentProps<typeof Badge> & {
  className?: string
  priceLevel: PriceLevelType
}

const PriceLevel = ({ priceLevel, className, ...props }: Props) => (
  <Badge className={cn('bg-success-bg/50', className)} {...props}>
    {getPriceLevels(priceLevel).map(({ filled }, index) => (
      <DollarSign
        key={index}
        size={12}
        className={filled ? 'text-emerald-400' : 'text-accent-text'}
      />
    ))}
  </Badge>
)

export default PriceLevel
