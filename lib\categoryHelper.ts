import { categories, Category, Recommendation } from '@/types/types'
import { categoryDetails } from '@/data/categories'
import { mockRecommendations } from '@/data/mockData'

export const getCategoryDetailsById = (category: Category) => {
  if (!categories.includes(category)) {
    throw new Error(`Invalid category: ${category}`)
  }

  return categoryDetails[category]
}

export const getCategoryDetailsByRecommendationId = (id: Recommendation['id']) => {
  for (const category of Object.keys(mockRecommendations) as Category[]) {
    const recommendation = mockRecommendations[category].find(recom => recom.id === id)
    if (recommendation) return categoryDetails[category]
  }
  throw new Error(`Recommendation not found: ${id}`)
}
