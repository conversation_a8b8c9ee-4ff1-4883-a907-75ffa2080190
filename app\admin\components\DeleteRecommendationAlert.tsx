'use client'

import {
  AlertD<PERSON>og,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/UI/alert-dialog'

type Props = {
  isOpen: boolean
  setIsOpen: (open: boolean) => void
  onConfirmDelete: () => void
}

const DeleteRecommendationAlert = ({ isOpen, setIsOpen, onConfirmDelete }: Props) => (
  <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
    <AlertDialogContent className="bg-white rounded-lg">
      <AlertDialogHeader>
        <AlertDialogTitle className="text-gray-900 text-lg font-semibold">
          Delete Recommendation
        </AlertDialogTitle>
        <AlertDialogDescription className="text-gray-600">
          Are you sure you want to delete this recommendation? This action cannot be undone and will
          permanently remove all associated data.
        </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter className="gap-2">
        <AlertDialogCancel onClick={() => setIsOpen(false)} className="rounded-lg">
          Cancel
        </AlertDialogCancel>
        <AlertDialogAction
          onClick={onConfirmDelete}
          className="bg-red-600 hoverActive:!bg-red-700 text-white rounded-lg"
        >
          Delete
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
)

export default DeleteRecommendationAlert
