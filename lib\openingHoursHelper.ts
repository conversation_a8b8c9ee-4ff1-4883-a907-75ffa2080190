import { DAYS } from '@/data/date'
import { DayHours, OpeningHoursData, TimePeriod } from '@/types/types'

export const generateDayHours = (dayHourds: DayHours | undefined) => {
  if (!dayHourds) return ''
  if (dayHourds.is24Hours) return '24h'
  if (dayHourds.periods.length === 1) {
    return `${dayHourds.periods[0].openTime}-${dayHourds.periods[0].closeTime}`
  } else {
    return dayHourds.periods.map(p => `${p.openTime}-${p.closeTime}`).join(', ')
  }
}

export const generateHoursString = (hoursData: OpeningHoursData): string => {
  const openDays = DAYS.filter(
    day =>
      hoursData[day.label] &&
      (hoursData[day.label]?.is24Hours || hoursData[day.label]?.periods?.length !== 0)
  )

  if (openDays.length === 0) return ''

  const all24Hours = openDays.every(day => hoursData[day.label]?.is24Hours)
  if (all24Hours && openDays.length === 7) {
    return '24/7'
  }

  const groups: { days: string[]; hours: string }[] = []
  let currentGroup: { days: string[]; hours: string } | null = null

  openDays.forEach(day => {
    const dayData = hoursData[day.label]
    const hoursStr = generateDayHours(dayData)

    if (currentGroup && currentGroup.hours === hoursStr) {
      currentGroup.days.push(day.short)
    } else {
      if (currentGroup) groups.push(currentGroup)
      currentGroup = { days: [day.short], hours: hoursStr }
    }
  })

  if (currentGroup) groups.push(currentGroup)

  const areConsecutive = (days: string[]): boolean => {
    if (days.length <= 1) return true

    const dayIndices = days.map(day => DAYS.findIndex(d => d.short === day))
    dayIndices.sort((a, b) => a - b)

    for (let i = 1; i < dayIndices.length; i++) {
      if (dayIndices[i] - dayIndices[i - 1] !== 1) {
        return false
      }
    }
    return true
  }

  return groups
    .map(group => {
      const daysStr =
        group.days.length === 1
          ? `${group.days[0]}`
          : areConsecutive(group.days)
            ? `${group.days[0]}-${group.days[group.days.length - 1]}`
            : group.days.join(', ')

      return `${daysStr}: ${group.hours}`
    })
    .join('; ')
}

export const timeToMinutes = (time: string): number => {
  const [hour, minutes] = time.split(':').map(Number)
  return hour * 60 + minutes
}

export const validatePeriodTimes = (openTime: string, closeTime: string) => {
  const openMinutes = timeToMinutes(openTime)
  const closeMinutes = timeToMinutes(closeTime)

  if (openMinutes === closeMinutes) {
    return { isValid: false, message: 'Opening and closing times cannot be the same' }
  }

  // Normal same-day period: open time is before close time
  if (openMinutes < closeMinutes) {
    return { isValid: true }
  }

  // Potential overnight period: open time is after close time
  const isValidOvernightOpen = openMinutes >= 1080 // 6 PM or later
  const isValidOvernightClose = closeMinutes <= 480 // 8 AM or earlier

  if (!isValidOvernightOpen || !isValidOvernightClose) {
    return {
      isValid: false,
      message:
        openMinutes > closeMinutes
          ? 'Invalid time range. For overnight hours, open after 6 PM and close before 8 AM'
          : 'Opening time must be before closing time',
    }
  }

  return { isValid: true }
}

export const checkPeriodsOverlap = (periods: TimePeriod[]) => {
  for (let i = 0; i < periods.length - 1; i++) {
    for (let j = i + 1; j < periods.length; j++) {
      const period1 = periods[i]
      const period2 = periods[j]

      const p1Start = timeToMinutes(period1.openTime)
      const p1End = timeToMinutes(period1.closeTime)
      const p2Start = timeToMinutes(period2.openTime)
      const p2End = timeToMinutes(period2.closeTime)

      // Check for overlap (accounting for overnight periods)
      const p1IsOvernight = p1End < p1Start && p1Start >= 1080 && p1End <= 480
      const p2IsOvernight = p2End < p2Start && p2Start >= 1080 && p2End <= 480

      let overlap = false

      if (!p1IsOvernight && !p2IsOvernight) {
        // Both are same-day periods
        overlap = p1Start < p2End && p1End > p2Start
      } else if (p1IsOvernight && !p2IsOvernight) {
        // Period 1 is overnight, period 2 is same-day
        overlap = p2Start < p1End || p2End > p1Start
      } else if (!p1IsOvernight && p2IsOvernight) {
        // Period 1 is same-day, period 2 is overnight
        overlap = p1Start < p2End || p1End > p2Start
      } else if (p1IsOvernight && p2IsOvernight) {
        // Both are overnight periods - they definitely overlap
        overlap = true
      }

      if (overlap) {
        return { hasOverlap: true, message: 'Time periods cannot overlap' }
      }
    }
  }

  return { hasOverlap: false }
}
