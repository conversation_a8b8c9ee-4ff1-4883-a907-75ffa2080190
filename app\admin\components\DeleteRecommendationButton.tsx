'use client'

import IconButton from '@/components/IconButton'
import { Trash2 } from 'lucide-react'
import { MouseEvent, useState } from 'react'
import DeleteRecommendationAlert from './DeleteRecommendationAlert'
import { toast } from 'sonner'

type Props = {
  id: string
}

const DeleteRecommendationButton = ({ id }: Props) => {
  const [deleteAlertIsOpen, setDeleteAlertIsOpen] = useState(false)

  const handleClickDelete = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    setDeleteAlertIsOpen(true)
  }

  const handleRecommendationDelete = () => {
    toast.success('Recommendation deleted: ' + id)
    // TODO: Add delete logic with API call
  }

  return (
    <>
      <IconButton
        variant="card"
        Icon={Trash2}
        onClick={handleClickDelete}
        className="p-2 h-auto hoverActive:text-red-600"
      />
      <DeleteRecommendationAlert
        isOpen={deleteAlertIsOpen}
        setIsOpen={setDeleteAlertIsOpen}
        onConfirmDelete={handleRecommendationDelete}
      />
    </>
  )
}

export default DeleteRecommendationButton
